from flask import render_template, request, url_for, redirect, flash, send_file
from datetime import datetime, timedelta
import pandas as pd
import os
import tempfile
import calendar
from collections import defaultdict

# هذا الملف يحتوي على وظائف التقارير التي سيتم إضافتها إلى app.py

def attendance_report():
    """عرض تقرير الحضور مع إمكانية التصفية والبحث"""
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status')
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    
    # استعلام قاعدة البيانات للحصول على سجلات الحضور
    query = Attendance.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Attendance.employee_id == filter_employee_id)
    if filter_status:
        query = query.filter(Attendance.status == filter_status)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Attendance.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Attendance.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # ترتيب النتائج حسب التاريخ تنازلياً
    attendance_records = query.order_by(Attendance.date.desc()).all()
    
    # حساب الإحصائيات
    stats = {
        'present': query.filter(Attendance.status == 'حاضر').count(),
        'absent': query.filter(Attendance.status == 'غائب').count(),
        'leave': query.filter(Attendance.status == 'إجازة').count(),
        'mission': query.filter(Attendance.status == 'مهمة رسمية').count()
    }
    
    # حساب إحصائيات الحضور الشهرية
    monthly_stats = calculate_monthly_attendance_stats(query)
    
    # إعداد ملخص الحضور حسب الموظف
    employee_summary = calculate_employee_attendance_summary(query)
    
    # الحصول على قوائم الموظفين والأقسام للتصفية
    all_employees = Employee.query.order_by(Employee.full_name).all()
    all_departments = Department.query.order_by(Department.name).all()
    
    return render_template('attendance_report.html',
                          attendance_records=attendance_records,
                          stats=stats,
                          monthly_stats=monthly_stats,
                          employee_summary=employee_summary,
                          all_employees=all_employees,
                          all_departments=all_departments,
                          filter_employee_id=filter_employee_id,
                          filter_status=filter_status,
                          filter_date_from=filter_date_from,
                          filter_date_to=filter_date_to,
                          filter_department_id=filter_department_id)

def calculate_monthly_attendance_stats(query):
    """حساب إحصائيات الحضور الشهرية"""
    # إنشاء قاموس لتخزين عدد أيام الحضور والغياب لكل شهر
    monthly_present = defaultdict(int)
    monthly_total = defaultdict(int)
    
    # الحصول على جميع سجلات الحضور
    all_records = query.all()
    
    # حساب عدد أيام الحضور والإجمالي لكل شهر
    for record in all_records:
        month = record.date.month - 1  # الفهرس يبدأ من 0
        monthly_total[month] += 1
        if record.status == 'حاضر':
            monthly_present[month] += 1
    
    # حساب نسبة الحضور لكل شهر
    monthly_rates = [0] * 12
    for month in range(12):
        if monthly_total[month] > 0:
            monthly_rates[month] = round((monthly_present[month] / monthly_total[month]) * 100)
    
    return monthly_rates

def calculate_employee_attendance_summary(query):
    """حساب ملخص الحضور لكل موظف"""
    # إنشاء قاموس لتخزين بيانات الحضور لكل موظف
    employee_data = defaultdict(lambda: {
        'present_days': 0,
        'absent_days': 0,
        'leave_days': 0,
        'mission_days': 0,
        'total_days': 0
    })
    
    # الحصول على جميع سجلات الحضور
    all_records = query.all()
    
    # حساب عدد أيام كل حالة لكل موظف
    for record in all_records:
        employee_id = record.employee_id
        employee_data[employee_id]['total_days'] += 1
        
        if record.status == 'حاضر':
            employee_data[employee_id]['present_days'] += 1
        elif record.status == 'غائب':
            employee_data[employee_id]['absent_days'] += 1
        elif record.status == 'إجازة':
            employee_data[employee_id]['leave_days'] += 1
        elif record.status == 'مهمة رسمية':
            employee_data[employee_id]['mission_days'] += 1
    
    # إعداد قائمة ملخص الحضور لكل موظف
    employee_summary = []
    for employee_id, data in employee_data.items():
        employee = Employee.query.get(employee_id)
        if employee:
            # حساب نسبة الحضور
            attendance_percentage = 0
            if data['total_days'] > 0:
                attendance_percentage = round((data['present_days'] / data['total_days']) * 100)
            
            employee_summary.append({
                'employee_name': employee.full_name,
                'employee_id': employee.employee_id,
                'department': employee.department.name if employee.department else 'غير محدد',
                'present_days': data['present_days'],
                'absent_days': data['absent_days'],
                'leave_days': data['leave_days'],
                'mission_days': data['mission_days'],
                'attendance_percentage': attendance_percentage
            })
    
    # ترتيب الملخص حسب نسبة الحضور تنازلياً
    employee_summary.sort(key=lambda x: x['attendance_percentage'], reverse=True)
    
    return employee_summary

def export_attendance_report():
    """تصدير تقرير الحضور إلى ملف Excel"""
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status')
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    
    # استعلام قاعدة البيانات للحصول على سجلات الحضور
    query = Attendance.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Attendance.employee_id == filter_employee_id)
    if filter_status:
        query = query.filter(Attendance.status == filter_status)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Attendance.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Attendance.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # ترتيب النتائج حسب التاريخ تنازلياً
    attendance_records = query.order_by(Attendance.date.desc()).all()
    
    # إنشاء DataFrame لتقرير الحضور
    data = []
    for record in attendance_records:
        data.append({
            'اسم الموظف': record.employee.full_name,
            'الرقم الوظيفي': record.employee.employee_id,
            'القسم': record.employee.department.name if record.employee.department else 'غير محدد',
            'التاريخ': record.date.strftime('%Y-%m-%d'),
            'الحالة': record.status,
            'ملاحظات': record.notes or ''
        })
    
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel مؤقت
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    # حفظ البيانات في ملف Excel
    df.to_excel(temp_filename, index=False, engine='openpyxl')
    
    # إرسال الملف للتنزيل
    return send_file(temp_filename, 
                     as_attachment=True,
                     download_name='تقرير_الحضور.xlsx',
                     mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

def departure_report():
    """عرض تقرير المغادرة مع إمكانية التصفية والبحث"""
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    filter_time_from = request.args.get('time_from')
    filter_time_to = request.args.get('time_to')
    
    # استعلام قاعدة البيانات للحصول على سجلات المغادرة
    query = Departure.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Departure.employee_id == filter_employee_id)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Departure.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Departure.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # تطبيق تصفية الوقت إذا تم تحديدها
    departure_records = query.order_by(Departure.date.desc()).all()
    if filter_time_from or filter_time_to:
        filtered_records = []
        for record in departure_records:
            time_str = record.departure_time.strftime('%H:%M')
            if filter_time_from and filter_time_to:
                if filter_time_from <= time_str <= filter_time_to:
                    filtered_records.append(record)
            elif filter_time_from:
                if filter_time_from <= time_str:
                    filtered_records.append(record)
            elif filter_time_to:
                if time_str <= filter_time_to:
                    filtered_records.append(record)
        departure_records = filtered_records
    
    # حساب الإحصائيات
    today = datetime.now().date()
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)
    
    stats = {
        'today': sum(1 for r in departure_records if r.date == today),
        'week': sum(1 for r in departure_records if r.date >= week_start),
        'month': sum(1 for r in departure_records if r.date >= month_start),
        'total': len(departure_records)
    }
    
    # حساب إحصائيات المغادرة الشهرية
    monthly_stats = calculate_monthly_departure_stats(query)
    
    # إعداد بيانات الخريطة الحرارية
    heatmap_data = calculate_departure_heatmap_data(query)
    
    # إعداد ملخص المغادرة حسب الموظف
    employee_summary = calculate_employee_departure_summary(query)
    
    # الحصول على قوائم الموظفين والأقسام للتصفية
    all_employees = Employee.query.order_by(Employee.full_name).all()
    all_departments = Department.query.order_by(Department.name).all()
    
    return render_template('departure_report.html',
                          departure_records=departure_records,
                          stats=stats,
                          monthly_stats=monthly_stats,
                          heatmap_data=heatmap_data,
                          employee_summary=employee_summary,
                          all_employees=all_employees,
                          all_departments=all_departments,
                          filter_employee_id=filter_employee_id,
                          filter_date_from=filter_date_from,
                          filter_date_to=filter_date_to,
                          filter_department_id=filter_department_id,
                          filter_time_from=filter_time_from,
                          filter_time_to=filter_time_to)

def calculate_monthly_departure_stats(query):
    """حساب إحصائيات المغادرة الشهرية"""
    # إنشاء قاموس لتخزين عدد المغادرات لكل شهر
    monthly_departures = [0] * 12
    
    # الحصول على جميع سجلات المغادرة
    all_records = query.all()
    
    # حساب عدد المغادرات لكل شهر
    for record in all_records:
        month = record.date.month - 1  # الفهرس يبدأ من 0
        monthly_departures[month] += 1
    
    return monthly_departures

def calculate_departure_heatmap_data(query):
    """إعداد بيانات الخريطة الحرارية لأوقات المغادرة"""
    # إنشاء مصفوفة لتخزين عدد المغادرات لكل يوم وساعة
    heatmap_data = [[0 for _ in range(8)] for _ in range(5)]  # 5 أيام × 8 ساعات
    
    # الحصول على جميع سجلات المغادرة
    all_records = query.all()
    
    # حساب عدد المغادرات لكل يوم وساعة
    for record in all_records:
        weekday = record.date.weekday()
        if weekday < 5:  # فقط أيام الأسبوع (الأحد إلى الخميس)
            hour = record.departure_time.hour
            if 8 <= hour < 16:  # ساعات العمل (8 صباحاً إلى 4 مساءً)
                hour_index = hour - 8
                heatmap_data[weekday][hour_index] += 1
    
    return heatmap_data

def calculate_employee_departure_summary(query):
    """حساب ملخص المغادرة لكل موظف"""
    # إنشاء قاموس لتخزين بيانات المغادرة لكل موظف
    employee_data = defaultdict(lambda: {
        'departure_count': 0,
        'departure_times': [],
        'departure_days': defaultdict(int)
    })
    
    # الحصول على جميع سجلات المغادرة
    all_records = query.all()
    
    # حساب بيانات المغادرة لكل موظف
    for record in all_records:
        employee_id = record.employee_id
        employee_data[employee_id]['departure_count'] += 1
        employee_data[employee_id]['departure_times'].append(record.departure_time)
        weekday = record.date.weekday()
        employee_data[employee_id]['departure_days'][weekday] += 1
    
    # إعداد قائمة ملخص المغادرة لكل موظف
    employee_summary = []
    weekday_names = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
    
    for employee_id, data in employee_data.items():
        employee = Employee.query.get(employee_id)
        if employee:
            # حساب متوسط وقت المغادرة
            average_time = '00:00'
            earliest_time = '00:00'
            most_frequent_day = 'غير محدد'
            
            if data['departure_times']:
                # حساب متوسط الوقت
                total_seconds = sum(dt.hour * 3600 + dt.minute * 60 + dt.second for dt in data['departure_times'])
                avg_seconds = total_seconds // len(data['departure_times'])
                avg_hours, remainder = divmod(avg_seconds, 3600)
                avg_minutes, avg_seconds = divmod(remainder, 60)
                average_time = f'{avg_hours:02d}:{avg_minutes:02d}'
                
                # تحديد أبكر وقت مغادرة
                earliest = min(data['departure_times'], key=lambda x: x.hour * 60 + x.minute)
                earliest_time = earliest.strftime('%H:%M')
            
            # تحديد أكثر يوم للمغادرة
            if data['departure_days']:
                most_frequent_day_index = max(data['departure_days'], key=data['departure_days'].get)
                most_frequent_day = weekday_names[most_frequent_day_index]
            
            employee_summary.append({
                'employee_name': employee.full_name,
                'employee_id': employee.employee_id,
                'department': employee.department.name if employee.department else 'غير محدد',
                'departure_count': data['departure_count'],
                'average_time': average_time,
                'earliest_time': earliest_time,
                'most_frequent_day': most_frequent_day
            })
    
    # ترتيب الملخص حسب عدد المغادرات تنازلياً
    employee_summary.sort(key=lambda x: x['departure_count'], reverse=True)
    
    return employee_summary

def export_departure_report():
    """تصدير تقرير المغادرة إلى ملف Excel"""
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    filter_time_from = request.args.get('time_from')
    filter_time_to = request.args.get('time_to')
    
    # استعلام قاعدة البيانات للحصول على سجلات المغادرة
    query = Departure.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Departure.employee_id == filter_employee_id)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Departure.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Departure.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # الحصول على سجلات المغادرة
    departure_records = query.order_by(Departure.date.desc()).all()
    
    # تطبيق تصفية الوقت إذا تم تحديدها
    if filter_time_from or filter_time_to:
        filtered_records = []
        for record in departure_records:
            time_str = record.departure_time.strftime('%H:%M')
            if filter_time_from and filter_time_to:
                if filter_time_from <= time_str <= filter_time_to:
                    filtered_records.append(record)
            elif filter_time_from:
                if filter_time_from <= time_str:
                    filtered_records.append(record)
            elif filter_time_to:
                if time_str <= filter_time_to:
                    filtered_records.append(record)
        departure_records = filtered_records
    
    # إنشاء DataFrame لتقرير المغادرة
    data = []
    for record in departure_records:
        data.append({
            'اسم الموظف': record.employee.full_name,
            'الرقم الوظيفي': record.employee.employee_id,
            'القسم': record.employee.department.name if record.employee.department else 'غير محدد',
            'التاريخ': record.date.strftime('%Y-%m-%d'),
            'وقت المغادرة': record.departure_time.strftime('%H:%M'),
            'سبب المغادرة': record.reason or ''
        })
    
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel مؤقت
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    # حفظ البيانات في ملف Excel
    df.to_excel(temp_filename, index=False, engine='openpyxl')
    
    # إرسال الملف للتنزيل
    return send_file(temp_filename, 
                     as_attachment=True,
                     download_name='تقرير_المغادرة.xlsx',
                     mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')