<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل القسم</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f7f6;
            color: #333;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 0;
        }
        .navbar {
            background-color: #2c3e50;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            color: #1abc9c !important;
            font-weight: bold;
            font-size: 1.5em;
        }
        .navbar-nav .nav-link {
            color: #ecf0f1 !important;
            margin: 0 10px;
            transition: color 0.3s ease;
        }
        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: #1abc9c !important;
        }
        .navbar-nav .nav-link i {
            margin-left: 5px;
        }
        .container {
            padding: 30px 15px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #34495e;
            color: #fff;
            font-size: 1.5em;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding: 15px 20px;
        }
        .card-body {
            padding: 25px;
        }
        .form-control {
            border-radius: 5px;
            border: 1px solid #ddd;
            padding: 10px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .form-control:focus {
            border-color: #1abc9c;
            box-shadow: 0 0 0 0.2rem rgba(26, 188, 156, 0.25);
        }
        .form-group label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #555;
        }
        .btn-primary {
            background-color: #1abc9c;
            border-color: #1abc9c;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #16a085;
            border-color: #16a085;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: #95a5a6;
            border-color: #95a5a6;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
            border-color: #7f8c8d;
            transform: translateY(-2px);
        }
        .btn i {
            margin-left: 5px;
        }
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            .card-header {
                font-size: 1.3em;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <a class="navbar-brand" href="#">نظام إدارة الموظفين</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ml-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard">الرئيسية <i class="fas fa-home"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/view_employees">الموظفون <i class="fas fa-users"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/manage_departments">الأقسام <i class="fas fa-building"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/logout">تسجيل الخروج <i class="fas fa-sign-out-alt"></i></a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-edit"></i> تعديل القسم: {{ department.name }}
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('edit_department', department_id=department.id) }}">
                            <div class="form-group">
                                <label for="name"><i class="fas fa-tag"></i> اسم القسم:</label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ department.name }}" required>
                            </div>
                            <div class="form-group">
                                <label for="description"><i class="fas fa-align-left"></i> الوصف (اختياري):</label>
                                <textarea class="form-control" id="description" name="description" rows="3">{{ department.description }}</textarea>
                            </div>
                            <div class="form-group">
                                <label for="manager_id"><i class="fas fa-user-tie"></i> مدير القسم (اختياري):</label>
                                <select class="form-control" id="manager_id" name="manager_id">
                                    <option value="">-- اختر مديرًا --</option>
                                    {% for employee in employees %}
                                        <option value="{{ employee.id }}" {% if department.manager_id == employee.id %}selected{% endif %}>{{ employee.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-group mt-4 text-center">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> حفظ التغييرات</button>
                                <a href="{{ url_for('manage_departments') }}" class="btn btn-secondary mr-2"><i class="fas fa-times"></i> إلغاء</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>