<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المغادرة</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            font-family: 'Tajawal', 'Arial', sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            transition: all 0.3s ease;
        }
        .wrapper {
            display: flex;
            width: 100%;
            align-items: stretch;
        }
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            background: #2c3e50;
            color: #fff;
            transition: all 0.3s;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        #sidebar.active {
            margin-right: -250px;
        }
        #sidebar .sidebar-header {
            padding: 20px;
            background: #34495e;
            text-align: center;
        }
        #sidebar .sidebar-header h3 {
            color: #fff;
            margin: 0;
            font-size: 1.5em;
        }
        #sidebar ul.components {
            padding: 20px 0;
            border-bottom: 1px solid #3a536e;
        }
        #sidebar ul li a {
            padding: 15px 20px;
            font-size: 1.1em;
            display: block;
            color: #e0e0e0;
            border-bottom: 1px solid rgba(255,255,255,0.05);
            transition: all 0.3s ease;
        }
        #sidebar ul li a:hover {
            color: #fff;
            background: #1abc9c;
            text-decoration: none;
            transform: translateX(-5px);
        }
        #sidebar ul li.active > a, a[aria-expanded="true"] {
            color: #fff;
            background: #16a085;
        }
        #content {
            width: 100%;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s;
        }
        .navbar {
            background-color: #fff;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .card {
            margin-bottom: 20px;
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.8rem 2rem rgba(0, 0, 0, 0.12);
        }
        .card-header {
            background-color: #3498db;
            color: #fff;
            border-bottom: none;
            border-top-left-radius: 0.75rem;
            border-top-right-radius: 0.75rem;
            font-size: 1.25rem;
            padding: 1rem 1.25rem;
        }
        .table thead th {
            background-color: #3498db;
            color: #fff;
            border-color: #3498db;
            vertical-align: middle;
            padding: 0.75rem;
        }
        .table tbody tr {
            transition: background-color 0.2s ease;
        }
        .table tbody tr:hover {
            background-color: #f5f5f5;
        }
        .table-bordered th, .table-bordered td {
            border: 1px solid #dee2e6;
            vertical-align: middle;
        }
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #28a745;
            border-color: #28a745;
        }
        .btn-primary:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        .btn-success {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }
        .btn-success:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        .btn-info {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #343a40;
        }
        .btn-info:hover {
            background-color: #e0a800;
            border-color: #d39e00;
        }
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        .form-control {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        .alert {
            border-radius: 0.5rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
        }
        .badge {
            padding: 0.5em 0.75em;
            border-radius: 0.5rem;
            font-weight: 500;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #343a40;
        }
        .badge-info {
            background-color: #17a2b8;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 5px;
        }
        .action-buttons .btn {
            width: 36px;
            height: 36px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .search-form {
            background-color: #fff;
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }
        .departure-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        .stat-card {
            flex: 1;
            min-width: 200px;
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.25rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }
        .stat-card .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 0.75rem;
        }
        .stat-card .stat-value {
            font-size: 1.75rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .stat-card .stat-label {
            color: #6c757d;
            font-size: 1rem;
        }
        .stat-card.today {
            border-top: 4px solid #28a745;
        }
        .stat-card.today .stat-icon {
            color: #28a745;
        }
        .stat-card.week {
            border-top: 4px solid #ffc107;
        }
        .stat-card.week .stat-icon {
            color: #ffc107;
        }
        .stat-card.month {
            border-top: 4px solid #17a2b8;
        }
        .stat-card.month .stat-icon {
            color: #17a2b8;
        }
        .stat-card.total {
            border-top: 4px solid #6f42c1;
        }
        .stat-card.total .stat-icon {
            color: #6f42c1;
        }
        @media (max-width: 768px) {
            #sidebar {
                margin-right: -250px;
            }
            #sidebar.active {
                margin-right: 0;
            }
            #sidebarCollapse span {
                display: none;
            }
            .departure-stats {
                flex-direction: column;
            }
            .stat-card {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header text-center">
                <h3>نظام إدارة الموارد البشرية</h3>
            </div>
            <ul class="list-unstyled components">
                <li>
                    <a href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> لوحة التحكم</a>
                </li>
                <li>
                    <a href="{{ url_for('view_employees') }}"><i class="fas fa-users"></i> إدارة الموظفين</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departments') }}"><i class="fas fa-building"></i> إدارة الأقسام</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_attendance') }}"><i class="fas fa-check-circle"></i> إدارة الحضور</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_documents') }}"><i class="fas fa-file-alt"></i> الوثائق</a>
                </li>
                <li class="active">
                    <a href="{{ url_for('manage_departure') }}"><i class="fas fa-plane-departure"></i> المغادرات</a>
                </li>
                <li>
                    <a href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-align-right"></i>
                        <span>تبديل الشريط الجانبي</span>
                    </button>
                    <div class="ml-auto">
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </nav>

            <h2 class="mb-4 text-center">إدارة سجلات المغادرة</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Departure Statistics -->
            <div class="departure-stats">
                <div class="stat-card today">
                    <div class="stat-icon"><i class="fas fa-calendar-day"></i></div>
                    <div class="stat-value">{{ stats.today if stats else 0 }}</div>
                    <div class="stat-label">مغادرات اليوم</div>
                </div>
                <div class="stat-card week">
                    <div class="stat-icon"><i class="fas fa-calendar-week"></i></div>
                    <div class="stat-value">{{ stats.week if stats else 0 }}</div>
                    <div class="stat-label">مغادرات الأسبوع</div>
                </div>
                <div class="stat-card month">
                    <div class="stat-icon"><i class="fas fa-calendar-alt"></i></div>
                    <div class="stat-value">{{ stats.month if stats else 0 }}</div>
                    <div class="stat-label">مغادرات الشهر</div>
                </div>
                <div class="stat-card total">
                    <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="stat-value">{{ stats.total if stats else 0 }}</div>
                    <div class="stat-label">إجمالي المغادرات</div>
                </div>
            </div>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-plane-departure mr-2"></i> سجلات المغادرة</h5>
                    <div>
                        <a href="{{ url_for('add_departure') }}" class="btn btn-success"><i class="fas fa-plus"></i> إضافة سجل مغادرة جديد</a>
                        <a href="{{ url_for('departure_report_route') }}" class="btn btn-primary"><i class="fas fa-file-alt"></i> تقرير المغادرات</a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <div class="search-form">
                        <form class="form-row" method="GET" action="{{ url_for('manage_departure') }}">
                            <div class="form-group col-md-3">
                                <label for="search"><i class="fas fa-search"></i> بحث باسم الموظف</label>
                                <input type="text" class="form-control" id="search" name="search" placeholder="اسم الموظف" value="{{ search_query if search_query else '' }}">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="employee_id"><i class="fas fa-user"></i> تصفية حسب الموظف</label>
                                <select class="form-control" id="employee_id" name="employee_id">
                                    <option value="">اختر موظف...</option>
                                    {% for employee in all_employees %}
                                        <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>{{ employee.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-group col-md-3">
                                <label for="date"><i class="fas fa-calendar-alt"></i> تصفية حسب التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" value="{{ filter_date_str if filter_date_str else '' }}">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="time_range"><i class="fas fa-clock"></i> تصفية حسب وقت المغادرة</label>
                                <select class="form-control" id="time_range" name="time_range">
                                    <option value="">جميع الأوقات</option>
                                    <option value="morning" {% if filter_time_range == 'morning' %}selected{% endif %}>صباحاً (8:00 - 12:00)</option>
                                    <option value="afternoon" {% if filter_time_range == 'afternoon' %}selected{% endif %}>ظهراً (12:00 - 16:00)</option>
                                    <option value="evening" {% if filter_time_range == 'evening' %}selected{% endif %}>مساءً (بعد 16:00)</option>
                                </select>
                            </div>
                            <div class="form-group col-12 text-center mt-3">
                                <button type="submit" class="btn btn-primary mx-2"><i class="fas fa-search"></i> بحث وتصفية</button>
                                <a href="{{ url_for('manage_departure') }}" class="btn btn-secondary mx-2"><i class="fas fa-sync-alt"></i> مسح التصفية</a>
                            </div>
                        </form>
                    </div>

                    {% if departure_records %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover text-center">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>التاريخ</th>
                                        <th>وقت المغادرة</th>
                                        <th>سبب المغادرة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in departure_records %}
                                        <tr>
                                            <td>{{ record.employee.full_name }}</td>
                                            <td>{{ record.date.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ record.departure_time }}</td>
                                            <td>{{ record.reason if record.reason else 'غير محدد' }}</td>
                                            <td class="action-buttons">
                                                <a href="{{ url_for('edit_departure', departure_id=record.id) }}" class="btn btn-info btn-sm" title="تعديل"><i class="fas fa-edit"></i></a>
                                                <form action="{{ url_for('delete_departure', departure_id=record.id) }}" method="POST" style="display:inline;" onsubmit="return confirm('هل أنت متأكد أنك تريد حذف سجل المغادرة هذا؟');">
                                                    <button type="submit" class="btn btn-danger btn-sm" title="حذف"><i class="fas fa-trash"></i></button>
                                                </form>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center" role="alert">
                            لا توجد سجلات مغادرة لعرضها.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#sidebarCollapse').on('click', function () {
                $('#sidebar').toggleClass('active');
            });
        });
    </script>
</body>
</html>