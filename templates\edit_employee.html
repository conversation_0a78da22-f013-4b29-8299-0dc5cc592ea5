<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل بيانات الموظف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f0f2f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
            margin-top: 50px;
            margin-bottom: 50px;
        }
        .card {
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,.05);
            margin-bottom: 25px;
            border-radius: 10px;
            overflow: hidden;
        }
        .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            border-bottom: none;
            padding: 15px 20px;
            display: flex;
            align-items: center;
        }
        .card-header i {
            margin-left: 10px;
            font-size: 1.1rem;
        }
        .card-body {
            padding: 25px;
        }
        .form-label {
            font-weight: 600;
            color: #343a40;
            margin-bottom: 8px;
        }
        .form-control, .form-select {
            padding: 10px 15px;
            border-radius: 6px;
            border: 1px solid #ced4da;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .btn {
            font-weight: bold;
            padding: 10px 20px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }
        .alert {
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .photo-preview {
            width: 150px;
            height: 150px;
            border: 2px dashed #ced4da;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            overflow: hidden;
            position: relative;
        }
        .photo-preview img {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
        }
        .photo-preview-placeholder {
            color: #6c757d;
            text-align: center;
            padding: 10px;
        }
        .custom-file-upload {
            display: inline-block;
            cursor: pointer;
            padding: 8px 16px;
            background-color: #f8f9fa;
            border: 1px solid #ced4da;
            border-radius: 6px;
            transition: all 0.3s ease;
        }
        .custom-file-upload:hover {
            background-color: #e9ecef;
        }
        .required-field::after {
            content: ' *';
            color: red;
        }
        .page-title {
            color: #343a40;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 700;
            position: relative;
            padding-bottom: 15px;
        }
        .page-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: #007bff;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <h2 class="page-title">تعديل بيانات الموظف</h2>
        
        <form method="POST" action="/edit_employee/{{ employee.id }}" enctype="multipart/form-data">
            <div class="card">
                <div class="card-header"><i class="fas fa-user-alt"></i>البيانات الشخصية</div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <label for="full_name" class="form-label required-field">الاسم الكامل</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" value="{{ employee.full_name }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="national_id" class="form-label required-field">الرقم الوطني</label>
                            <input type="text" class="form-control" id="national_id" name="national_id" value="{{ employee.national_id }}" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 mb-3 mb-md-0">
                            <label for="age" class="form-label">العمر</label>
                            <input type="number" class="form-control" id="age" name="age" value="{{ employee.age }}">
                        </div>
                        <div class="col-md-4 mb-3 mb-md-0">
                            <label for="date_of_birth" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="{{ employee.date_of_birth }}">
                        </div>
                        <div class="col-md-4">
                            <label for="gender" class="form-label">الجنس</label>
                            <select class="form-select" id="gender" name="gender">
                                <option value="">اختر...</option>
                                <option value="ذكر" {% if employee.gender == 'ذكر' %}selected{% endif %}>ذكر</option>
                                <option value="أنثى" {% if employee.gender == 'أنثى' %}selected{% endif %}>أنثى</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-4 mb-3 mb-md-0">
                            <label for="birth_place_city" class="form-label">مدينة الميلاد</label>
                            <input type="text" class="form-control" id="birth_place_city" name="birth_place_city" value="{{ employee.birth_place_city }}">
                        </div>
                        <div class="col-md-4 mb-3 mb-md-0">
                            <label for="birth_place_region" class="form-label">منطقة الميلاد</label>
                            <input type="text" class="form-control" id="birth_place_region" name="birth_place_region" value="{{ employee.birth_place_region }}">
                        </div>
                        <div class="col-md-4">
                            <label for="birth_place_municipality" class="form-label">بلدية الميلاد</label>
                            <input type="text" class="form-control" id="birth_place_municipality" name="birth_place_municipality" value="{{ employee.birth_place_municipality }}">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="nationality" class="form-label">الجنسية</label>
                        <input type="text" class="form-control" id="nationality" name="nationality" value="{{ employee.nationality }}">
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header"><i class="fas fa-briefcase"></i>البيانات الوظيفية</div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <label for="employee_id" class="form-label required-field">الرقم الوظيفي</label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" value="{{ employee.employee_id }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="job_title" class="form-label">المسمى الوظيفي</label>
                            <input type="text" class="form-control" id="job_title" name="job_title" value="{{ employee.job_title }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <label for="appointment_type" class="form-label">نوع التعيين</label>
                            <select class="form-select" id="appointment_type" name="appointment_type">
                                <option value="">اختر...</option>
                                <option value="تعيين" {% if employee.appointment_type == 'تعيين' %}selected{% endif %}>تعيين</option>
                                <option value="عقد" {% if employee.appointment_type == 'عقد' %}selected{% endif %}>عقد</option>
                                <option value="ندب" {% if employee.appointment_type == 'ندب' %}selected{% endif %}>ندب</option>
                                <option value="تكليف" {% if employee.appointment_type == 'تكليف' %}selected{% endif %}>تكليف</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="appointment_date" class="form-label">تاريخ التعيين</label>
                            <input type="date" class="form-control" id="appointment_date" name="appointment_date" value="{{ employee.appointment_date }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <label for="start_work_date" class="form-label">تاريخ بدء العمل</label>
                            <input type="date" class="form-control" id="start_work_date" name="start_work_date" value="{{ employee.start_work_date }}">
                        </div>
                        <div class="col-md-6">
                            <label for="current_grade" class="form-label">الدرجة الحالية</label>
                            <input type="text" class="form-control" id="current_grade" name="current_grade" value="{{ employee.current_grade }}">
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6 mb-3 mb-md-0">
                            <label for="allowances_count" class="form-label">عدد العلاوات</label>
                            <input type="number" class="form-control" id="allowances_count" name="allowances_count" value="{{ employee.allowances_count }}">
                        </div>
                        <div class="col-md-6">
                            <label for="last_promotion_date" class="form-label">تاريخ آخر ترقية</label>
                            <input type="date" class="form-control" id="last_promotion_date" name="last_promotion_date" value="{{ employee.last_promotion_date }}">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="years_of_service" class="form-label">سنوات الخدمة</label>
                        <input type="number" class="form-control" id="years_of_service" name="years_of_service" value="{{ employee.years_of_service }}">
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header"><i class="fas fa-image"></i>صورة الموظف</div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3 mb-md-0">
                            <div class="photo-preview" id="photoPreview">
                                {% if employee.photo %}
                                <img src="{{ url_for('uploaded_file', filename=employee.photo) }}" alt="صورة الموظف">
                                {% else %}
                                <div class="photo-preview-placeholder">
                                    <i class="fas fa-user fa-3x mb-2"></i>
                                    <p>معاينة الصورة</p>
                                </div>
                                {% endif %}
                            </div>
                            <label class="custom-file-upload">
                                <input type="file" id="photo_file" name="photo_file" accept="image/*" style="display: none;" onchange="previewImage(this)">
                                <i class="fas fa-upload"></i> تغيير الصورة
                            </label>
                            <input type="hidden" id="photo" name="photo" value="{{ employee.photo }}">
                        </div>
                        <div class="col-md-8">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>ملاحظات:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>يفضل أن تكون الصورة بخلفية بيضاء</li>
                                    <li>الحجم المناسب للصورة: 300×300 بكسل</li>
                                    <li>الصيغ المدعومة: JPG, PNG, JPEG</li>
                                    <li>الحد الأقصى لحجم الملف: 2 ميجابايت</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <button type="submit" class="btn btn-success"><i class="fas fa-save me-2"></i>حفظ التغييرات</button>
                <a href="/view_employees" class="btn btn-secondary"><i class="fas fa-arrow-circle-left me-2"></i>العودة لقائمة الموظفين</a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function previewImage(input) {
            const preview = document.getElementById('photoPreview');
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    // إزالة العنصر الحالي (إما الصورة أو النص البديل)
                    while (preview.firstChild) {
                        preview.removeChild(preview.firstChild);
                    }
                    
                    // إنشاء عنصر الصورة وإضافته
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    preview.appendChild(img);
                    
                    // تحديث قيمة حقل الإدخال المخفي
                    document.getElementById('photo').value = input.files[0].name;
                }
                
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>