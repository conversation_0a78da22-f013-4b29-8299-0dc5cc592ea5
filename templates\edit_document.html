<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل وثيقة</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: #343a40 !important;
        }
        .sidebar {
            height: 100vh;
            background-color: #343a40;
            padding-top: 20px;
            color: white;
        }
        .sidebar .nav-link {
            color: white;
            padding: 10px 15px;
            transition: background-color 0.3s;
        }
        .sidebar .nav-link:hover {
            background-color: #495057;
        }
        .sidebar .nav-link.active {
            background-color: #007bff;
        }
        .content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">نظام شؤون الموظفين</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard"><i class="fas fa-home"></i> الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/view_employees"><i class="fas fa-users"></i> الموظفون</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/manage_departments"><i class="fas fa-building"></i> الأقسام</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/manage_attendance"><i class="fas fa-clock"></i> الحضور</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/manage_departure"><i class="fas fa-sign-out-alt"></i> الانصراف</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/manage_documents"><i class="fas fa-file-alt"></i> الوثائق</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/logout"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="/dashboard">
                                <i class="fas fa-home"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/view_employees">
                                <i class="fas fa-users"></i>
                                الموظفون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manage_departments">
                                <i class="fas fa-building"></i>
                                الأقسام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manage_attendance">
                                <i class="fas fa-clock"></i>
                                الحضور
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/manage_departure">
                                <i class="fas fa-sign-out-alt"></i>
                                الانصراف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/manage_documents">
                                <i class="fas fa-file-alt"></i>
                                الوثائق
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">تعديل وثيقة</h1>
                </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="card">
                    <div class="card-header bg-primary text-white"><i class="fas fa-edit"></i> تعديل وثيقة</div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('edit_document', document_id=document.id) }}" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="employee_id" class="form-label">الموظف:</label>
                                <select class="form-select" id="employee_id" name="employee_id" required>
                                    {% for employee in employees %}
                                        <option value="{{ employee.id }}" {% if document.employee_id == employee.id %}selected{% endif %}>{{ employee.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="document_type" class="form-label">نوع الوثيقة:</label>
                                <input type="text" class="form-control" id="document_type" name="document_type" value="{{ document.document_type }}" placeholder="مثال: عقد عمل، شهادة خبرة" required>
                            </div>
                            <div class="mb-3">
                                <label for="document_file" class="form-label">ملف الوثيقة:</label>
                                <input type="file" class="form-control" id="document_file" name="document_file">
                                <small class="form-text text-muted">اترك هذا الحقل فارغًا إذا كنت لا تريد تغيير الملف الحالي.</small>
                                {% if document.file_path %}
                                <div class="mt-2">
                                    <strong>الملف الحالي:</strong> <a href="{{ url_for('uploaded_file', filename=document.file_path) }}" target="_blank">{{ document.file_path }}</a>
                                </div>
                                {% endif %}
                            </div>
                            <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> تحديث الوثيقة</button>
                            <a href="{{ url_for('manage_documents') }}" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</a>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS and Popper.js -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>