<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الأقسام</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f7f6;
            color: #333;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 0;
            display: flex;
        }
        .sidebar {
            width: 250px;
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            height: 100vh;
            position: fixed;
            top: 0;
            right: 0; /* Adjusted for RTL */
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            align-items: flex-end; /* Adjusted for RTL */
            z-index: 1000;
        }
        .sidebar h2 {
            color: #1abc9c;
            margin-bottom: 30px;
            font-size: 1.8em;
            text-align: right; /* Adjusted for RTL */
            width: 100%;
        }
        .sidebar ul {
            list-style: none;
            padding: 0;
            width: 100%;
        }
        .sidebar ul li {
            margin-bottom: 15px;
        }
        .sidebar ul li a {
            color: #ecf0f1;
            text-decoration: none;
            font-size: 1.1em;
            display: block;
            padding: 10px 15px;
            border-radius: 5px;
            transition: background-color 0.3s ease, color 0.3s ease;
            text-align: right; /* Adjusted for RTL */
        }
        .sidebar ul li a:hover,
        .sidebar ul li a.active {
            background-color: #1abc9c;
            color: #fff;
        }
        .sidebar ul li a i {
            margin-left: 10px; /* Adjusted for RTL */
            margin-right: 0; /* Adjusted for RTL */
        }
        .content {
            margin-right: 250px; /* Adjusted for RTL, same as sidebar width */
            padding: 30px;
            flex-grow: 1;
            width: calc(100% - 250px);
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #34495e;
            color: #fff;
            font-size: 1.5em;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-body {
            padding: 25px;
        }
        .table-responsive {
            margin-top: 20px;
            overflow-x: auto;
        }
        .table {
            width: 100%;
            min-width: 800px; /* Ensure table doesn't shrink too much */
            margin-bottom: 1rem;
            color: #333;
            border-collapse: collapse;
            font-size: 0.95em;
        }
        .table th,
        .table td {
            padding: 12px 15px;
            vertical-align: middle;
            border-top: 1px solid #dee2e6;
            white-space: nowrap; /* Prevent text wrapping */
        }
        .table thead th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            font-weight: bold;
            color: #555;
            text-align: right; /* Adjusted for RTL */
        }
        .table tbody tr:hover {
            background-color: #f1f1f1;
        }
        .btn-primary {
            background-color: #1abc9c;
            border-color: #1abc9c;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #16a085;
            border-color: #16a085;
            transform: translateY(-2px);
        }
        .btn-info {
            background-color: #3498db;
            border-color: #3498db;
            transition: all 0.3s ease;
        }
        .btn-info:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-2px);
        }
        .btn-danger {
            background-color: #e74c3c;
            border-color: #e74c3c;
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background-color: #c0392b;
            border-color: #c0392b;
            transform: translateY(-2px);
        }
        .btn-sm {
            padding: 0.3rem 0.6rem;
            font-size: 0.8em;
        }
        .form-control {
            border-radius: 5px;
            border: 1px solid #ddd;
            padding: 10px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .form-control:focus {
            border-color: #1abc9c;
            box-shadow: 0 0 0 0.2rem rgba(26, 188, 156, 0.25);
        }
        .form-group label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #555;
        }
        .modal-content {
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }
        .modal-header {
            background-color: #34495e;
            color: #fff;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            border-bottom: none;
        }
        .modal-title {
            font-weight: bold;
        }
        .modal-footer {
            border-top: none;
        }
        .action-icons .btn {
            margin: 0 3px; /* Adjusted margin for RTL */
            padding: 6px 8px;
            font-size: 0.9em;
            border-radius: 5px;
        }
        .action-icons .btn i {
            margin-left: 5px; /* Adjusted margin for RTL */
            margin-right: 0; /* Adjusted for RTL */
        }
        .form-inline .form-group {
            margin-left: 15px; /* Adjusted for RTL */
            margin-right: 0; /* Adjusted for RTL */
        }
        .form-inline .btn {
             margin-left: 10px; /* Adjusted for RTL */
             margin-right: 0; /* Adjusted for RTL */
        }
        .form-inline .btn.ml-2 {
            margin-left: .5rem!important; /* Adjusted for RTL */
            margin-right: 0!important; /* Adjusted for RTL */
        }
        .department-card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
            overflow: hidden;
        }
        .department-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        }
        .department-card .card-header {
            padding: 15px;
            font-size: 1.2em;
        }
        .department-card .card-body {
            padding: 15px;
        }
        .department-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
            flex: 1;
            margin: 0 5px;
        }
        .stat-item i {
            font-size: 1.5em;
            margin-bottom: 5px;
            color: #1abc9c;
        }
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #34495e;
        }
        .stat-label {
            font-size: 0.8em;
            color: #7f8c8d;
        }
        .view-toggle {
            margin-bottom: 20px;
        }
        .view-toggle .btn {
            border-radius: 20px;
            padding: 8px 15px;
        }
        .view-toggle .btn.active {
            background-color: #1abc9c;
            color: white;
        }
        .badge-department {
            background-color: #3498db;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8em;
            margin-right: 5px;
        }
        .search-box {
            position: relative;
            margin-bottom: 20px;
        }
        .search-box .form-control {
            padding-right: 40px;
            border-radius: 20px;
        }
        .search-box .search-icon {
            position: absolute;
            right: 15px;
            top: 10px;
            color: #7f8c8d;
        }
        .filter-section {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .filter-section h5 {
            color: #34495e;
            margin-bottom: 15px;
            font-size: 1.1em;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
                box-shadow: none;
                align-items: center;
            }
            .sidebar h2 {
                text-align: center;
            }
            .sidebar ul {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
            }
            .sidebar ul li {
                margin: 0 10px 10px 10px;
            }
            .content {
                margin-right: 0;
                width: 100%;
            }
            .card-header {
                flex-direction: column;
                align-items: flex-start;
            }
            .card-header .btn {
                margin-top: 10px;
            }
            .table th,
            .table td {
                padding: 8px 10px;
                font-size: 0.85em;
            }
            .action-icons .btn {
                padding: 4px 6px;
                font-size: 0.8em;
            }
        }
        @media (max-width: 576px) {
            .form-inline .form-group {
                margin-left: 0; /* Adjusted for RTL */
                margin-right: 0; /* Adjusted for RTL */
                width: 100%;
                margin-bottom: 10px;
            }
            .form-inline .btn {
                width: 100%;
                margin-left: 0; /* Adjusted for RTL */
                margin-right: 0; /* Adjusted for RTL */
            }
            .form-inline .btn.ml-2 {
                margin-left: 0!important; /* Adjusted for RTL */
                margin-right: 0!important; /* Adjusted for RTL */
            }
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>نظام إدارة الموظفين</h2>
        <ul>
            <li><a href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> الرئيسية</a></li>
            <li><a href="{{ url_for('view_employees') }}"><i class="fas fa-users"></i> الموظفون</a></li>
            <li><a href="{{ url_for('manage_departments') }}" class="active"><i class="fas fa-building"></i> الأقسام</a></li>
            <li><a href="{{ url_for('manage_attendance') }}"><i class="fas fa-clock"></i> إدارة الدوام</a></li>
            <li><a href="{{ url_for('manage_documents') }}"><i class="fas fa-file-alt"></i> الوثائق</a></li>
            <li><a href="{{ url_for('manage_departure') }}"><i class="fas fa-plane-departure"></i> المغادرات</a></li>
            <li><a href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
        </ul>
    </div>

    <div class="content">
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-12 col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <span><i class="fas fa-building mr-2"></i> إدارة الأقسام</span>
                            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addDepartmentModal">
                                <i class="fas fa-plus"></i> إضافة قسم جديد
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="filter-section">
                                <h5><i class="fas fa-filter"></i> خيارات البحث والفلترة</h5>
                                <form method="GET" class="form-inline mb-4">
                                    <div class="form-group mx-sm-3 mb-2">
                                        <label for="search" class="sr-only">بحث</label>
                                        <div class="search-box">
                                            <input type="text" class="form-control" id="search" name="search" placeholder="البحث بالاسم أو الوصف" value="{{ search_query if search_query else '' }}">
                                            <i class="fas fa-search search-icon"></i>
                                        </div>
                                    </div>
                                    <div class="form-group mx-sm-3 mb-2">
                                        <label for="manager_id" class="sr-only">مدير القسم</label>
                                        <select class="form-control" id="manager_id" name="manager_id">
                                            <option value="">-- فلترة حسب المدير --</option>
                                            {% for employee in all_employees %}
                                                <option value="{{ employee.id }}" {% if filter_manager_id and filter_manager_id|int == employee.id %}selected{% endif %}>{{ employee.full_name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary mb-2"><i class="fas fa-search"></i> بحث وفلترة</button>
                                    <a href="{{ url_for('manage_departments') }}" class="btn btn-secondary mb-2 mr-2"><i class="fas fa-redo"></i> إعادة تعيين</a>
                                </form>
                            </div>
                            
                            <div class="view-toggle btn-group mb-4" role="group">
                                <button type="button" class="btn btn-outline-primary active" id="table-view-btn"><i class="fas fa-table"></i> عرض جدولي</button>
                                <button type="button" class="btn btn-outline-primary" id="card-view-btn"><i class="fas fa-th"></i> عرض بطاقات</button>
                            </div>
                            
                            <div id="table-view" class="table-responsive">
                                <table class="table table-hover table-striped">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>الوصف</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>عدد الموظفين</th>
                                            <th>مدير القسم</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for department in departments %}
                                        <tr>
                                            <td>{{ department.name }}</td>
                                            <td>{{ department.description }}</td>
                                            <td>{{ department.creation_date }}</td>
                                            <td>{{ department.employee_count }}</td>
                                            <td>{{ department.manager_name }}</td>
                                            <td class="action-icons">
                                                <a href="{{ url_for('view_department', department_id=department.id) }}" class="btn btn-info btn-sm" title="عرض"><i class="fas fa-eye"></i></a>
                                                <a href="{{ url_for('edit_department', department_id=department.id) }}" class="btn btn-primary btn-sm" title="تعديل"><i class="fas fa-edit"></i></a>
                                                <form action="{{ url_for('delete_department', department_id=department.id) }}" method="POST" style="display:inline;">
                                                    <button type="submit" class="btn btn-danger btn-sm" title="حذف" onclick="return confirm('هل أنت متأكد من حذف هذا القسم؟');"><i class="fas fa-trash"></i></button>
                                                </form>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center">لا توجد أقسام لعرضها.</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            
                            <div id="card-view" class="row" style="display: none;">
                                {% for department in departments %}
                                <div class="col-lg-4 col-md-6 col-sm-12">
                                    <div class="department-card card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5 class="mb-0">{{ department.name }}</h5>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-light" type="button" id="dropdownMenuButton{{ department.id }}" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownMenuButton{{ department.id }}">
                                                    <a class="dropdown-item" href="{{ url_for('view_department', department_id=department.id) }}"><i class="fas fa-eye"></i> عرض</a>
                                                    <a class="dropdown-item" href="{{ url_for('edit_department', department_id=department.id) }}"><i class="fas fa-edit"></i> تعديل</a>
                                                    <div class="dropdown-divider"></div>
                                                    <form action="{{ url_for('delete_department', department_id=department.id) }}" method="POST">
                                                        <button type="submit" class="dropdown-item text-danger" onclick="return confirm('هل أنت متأكد من حذف هذا القسم؟');"><i class="fas fa-trash"></i> حذف</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <p class="card-text">{{ department.description }}</p>
                                            <div class="department-stats">
                                                <div class="stat-item">
                                                    <i class="fas fa-users"></i>
                                                    <div class="stat-value">{{ department.employee_count }}</div>
                                                    <div class="stat-label">الموظفين</div>
                                                </div>
                                                <div class="stat-item">
                                                    <i class="fas fa-user-tie"></i>
                                                    <div class="stat-value">{{ department.manager_name }}</div>
                                                    <div class="stat-label">المدير</div>
                                                </div>
                                                <div class="stat-item">
                                                    <i class="fas fa-calendar-alt"></i>
                                                    <div class="stat-value">{{ department.creation_date.split(' ')[0] }}</div>
                                                    <div class="stat-label">تاريخ الإنشاء</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% else %}
                                <div class="col-12 text-center">
                                    <div class="alert alert-info">لا توجد أقسام لعرضها.</div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Department Modal -->
    <div class="modal fade" id="addDepartmentModal" tabindex="-1" role="dialog" aria-labelledby="addDepartmentModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDepartmentModalLabel"><i class="fas fa-plus-circle"></i> إضافة قسم جديد</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form method="POST" action="{{ url_for('add_department') }}">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="departmentName"><i class="fas fa-tag"></i> اسم القسم:</label>
                            <input type="text" class="form-control" id="departmentName" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="departmentDescription"><i class="fas fa-align-left"></i> الوصف (اختياري):</label>
                            <textarea class="form-control" id="departmentDescription" name="description" rows="3"></textarea>
                        </div>
                         <div class="form-group">
                            <label for="departmentManager"><i class="fas fa-user-tie"></i> مدير القسم (اختياري):</label>
                            <select class="form-control" id="departmentManager" name="manager_id">
                                <option value="">-- اختر مديرًا --</option>
                                {% for employee in all_employees %}
                                    <option value="{{ employee.id }}">{{ employee.full_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal"><i class="fas fa-times"></i> إلغاء</button>
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> إضافة القسم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // Toggle between table and card view
            $('#table-view-btn').click(function() {
                $(this).addClass('active');
                $('#card-view-btn').removeClass('active');
                $('#table-view').show();
                $('#card-view').hide();
            });
            
            $('#card-view-btn').click(function() {
                $(this).addClass('active');
                $('#table-view-btn').removeClass('active');
                $('#table-view').hide();
                $('#card-view').show();
            });
        });
    </script>
    </body>
    </html>