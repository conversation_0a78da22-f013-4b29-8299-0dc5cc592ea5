<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الإجازات</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .wrapper {
            display: flex;
            width: 100%;
            align-items: stretch;
        }
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            background: #343a40;
            color: #fff;
            transition: all 0.3s;
        }
        #sidebar.active {
            margin-right: -250px;
        }
        #sidebar .sidebar-header {
            padding: 20px;
            background: #343a40;
        }
        #sidebar ul.components {
            padding: 20px 0;
            border-bottom: 1px solid #47748b;
        }
        #sidebar ul p {
            color: #fff;
            padding: 10px;
        }
        #sidebar ul li a {
            padding: 10px;
            font-size: 1.1em;
            display: block;
            color: #fff;
        }
        #sidebar ul li a:hover {
            color: #343a40;
            background: #fff;
        }
        #content {
            width: 100%;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s;
        }
        .card {
            margin-bottom: 20px;
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        .card-header {
            background-color: #007bff;
            color: white;
            border-bottom: none;
            border-radius: 10px 10px 0 0;
        }
        .table th,
        .table td {
            text-align: right;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .modal-header {
            background-color: #007bff;
            color: white;
            border-bottom: none;
        }
        .modal-footer {
            border-top: none;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header text-center">
                <h3>نظام إدارة الموظفين</h3>
            </div>
            <ul class="list-unstyled components">
                <li>
                    <a href="{{ url_for('dashboard') }}">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('view_employees') }}">
                        <i class="fas fa-users"></i>
                        الموظفون
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departments') }}">
                        <i class="fas fa-building"></i>
                        الأقسام
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('manage_attendance') }}">
                        <i class="fas fa-check-circle"></i>
                        الحضور والانصراف
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departure') }}">
                        <i class="fas fa-plane-departure"></i>
                        المغادرات
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('manage_documents') }}">
                        <i class="fas fa-file-alt"></i>
                        المستندات
                    </a>
                </li>
                <li class="active">
                    <a href="{{ url_for('manage_leaves') }}">
                        <i class="fas fa-calendar-alt"></i>
                        الإجازات
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
                <button type="button" id="sidebarCollapse" class="btn btn-info">
                    <i class="fas fa-align-right"></i>
                    <span>تبديل الشريط الجانبي</span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav mr-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_employees') }}">الموظفون</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_departments') }}">الأقسام</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_attendance') }}">الحضور والانصراف</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_departure') }}">المغادرات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_documents') }}">المستندات</a>
                        </li>
                        <li class="nav-item active">
                            <a class="nav-link" href="{{ url_for('manage_leaves') }}">الإجازات <span class="sr-only">(الحالي)</span></a>
                        </li>
                    </ul>
                </div>
            </nav>

            <h2 class="mb-4">إدارة الإجازات</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="row">
                <div class="col-md-3">
                    <div class="card text-white bg-info mb-3">
                        <div class="card-body">
                            <h5 class="card-title">إجازات قيد الانتظار</h5>
                            <p class="card-text display-4">{{ pending_leaves_count }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-success mb-3">
                        <div class="card-body">
                            <h5 class="card-title">إجازات موافق عليها</h5>
                            <p class="card-text display-4">{{ approved_leaves_count }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-danger mb-3">
                        <div class="card-body">
                            <h5 class="card-title">إجازات مرفوضة</h5>
                            <p class="card-text display-4">{{ rejected_leaves_count }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-white bg-primary mb-3">
                        <div class="card-body">
                            <h5 class="card-title">إجمالي الإجازات</h5>
                            <p class="card-text display-4">{{ total_leaves_count }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card mb-4">
                <div class="card-header">
                    بحث وتصفية الإجازات
                </div>
                <div class="card-body">
                    <form class="form-inline mb-4 justify-content-center" method="GET" action="{{ url_for('manage_leaves') }}">
                        <div class="form-group mx-2 mb-2">
                            <label for="search" class="sr-only">بحث باسم الموظف</label>
                            <input type="text" class="form-control" id="search" name="search" placeholder="بحث باسم الموظف" value="{{ search_query if search_query else '' }}">
                        </div>
                        <div class="form-group mx-2 mb-2">
                            <label for="employee_id" class="sr-only">الموظف</label>
                            <select class="form-control" id="employee_id" name="employee_id">
                                <option value="">-- اختر الموظف --</option>
                                {% for employee in employees %}
                                <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>{{ employee.full_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group mx-2 mb-2">
                            <label for="status" class="sr-only">الحالة</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">-- اختر الحالة --</option>
                                <option value="Pending" {% if filter_status == 'Pending' %}selected{% endif %}>قيد الانتظار</option>
                                <option value="Approved" {% if filter_status == 'Approved' %}selected{% endif %}>موافق عليها</option>
                                <option value="Rejected" {% if filter_status == 'Rejected' %}selected{% endif %}>مرفوضة</option>
                            </select>
                        </div>
                        <div class="form-group mx-2 mb-2">
                            <label for="date_from" class="sr-only">من تاريخ</label>
                            <input type="date" class="form-control" id="date_from" name="date_from" placeholder="من تاريخ" value="{{ filter_date_from if filter_date_from else '' }}">
                        </div>
                        <div class="form-group mx-2 mb-2">
                            <label for="date_to" class="sr-only">إلى تاريخ</label>
                            <input type="date" class="form-control" id="date_to" name="date_to" placeholder="إلى تاريخ" value="{{ filter_date_to if filter_date_to else '' }}">
                        </div>
                        <button type="submit" class="btn btn-primary mx-2 mb-2"><i class="fas fa-search"></i> بحث وتصفية</button>
                        <a href="{{ url_for('manage_leaves') }}" class="btn btn-secondary mx-2 mb-2"><i class="fas fa-sync-alt"></i> مسح التصفية</a>
                    </form>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    قائمة الإجازات
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-primary mb-3" data-toggle="modal" data-target="#addLeaveModal">
                        <i class="fas fa-plus"></i> إضافة إجازة جديدة
                    </button>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع الإجازة</th>
                                    <th>تاريخ البدء</th>
                                    <th>تاريخ الانتهاء</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if leaves %}
                                    {% for leave in leaves %}
                                    <tr>
                                        <td>{{ leave.employee.full_name }}</td>
                                        <td>{{ leave.leave_type }}</td>
                                        <td>{{ leave.start_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ leave.end_date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            {% if leave.status == 'Pending' %}
                                                <span class="badge badge-warning">قيد الانتظار</span>
                                            {% elif leave.status == 'Approved' %}
                                                <span class="badge badge-success">موافق عليها</span>
                                            {% elif leave.status == 'Rejected' %}
                                                <span class="badge badge-danger">مرفوضة</span>
                                            {% else %}
                                                {{ leave.status }}
                                            {% endif %}
                                        </td>
                                        <td>{{ leave.request_date.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            <a href="{{ url_for('edit_leave', leave_id=leave.id) }}" class="btn btn-sm btn-warning"><i class="fas fa-edit"></i> تعديل</a>
                                            <form action="{{ url_for('delete_leave', leave_id=leave.id) }}" method="POST" style="display:inline;" onsubmit="return confirm('هل أنت متأكد من حذف طلب الإجازة هذا؟');">
                                                <button type="submit" class="btn btn-sm btn-danger"><i class="fas fa-trash"></i> حذف</button>
                                            </form>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center">لا توجد إجازات لعرضها</td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Leave Modal -->
    <div class="modal fade" id="addLeaveModal" tabindex="-1" role="dialog" aria-labelledby="addLeaveModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addLeaveModalLabel">إضافة إجازة جديدة</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form action="{{ url_for('add_leave') }}" method="POST">
                    <div class="modal-body">
                        <div class="form-group">
                            <label for="employee_id">الموظف</label>
                            <select class="form-control" id="employee_id" name="employee_id" required>
                                <option value="">-- اختر الموظف --</option>
                                {% for employee in employees %}
                                <option value="{{ employee.id }}">{{ employee.full_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="leave_type">نوع الإجازة</label>
                            <select class="form-control" id="leave_type" name="leave_type" required>
                                <option value="">-- اختر نوع الإجازة --</option>
                                <option value="سنوية">سنوية</option>
                                <option value="مرضية">مرضية</option>
                                <option value="عارضة">عارضة</option>
                                <option value="بدون راتب">بدون راتب</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="start_date">تاريخ البدء</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" required>
                        </div>
                        <div class="form-group">
                            <label for="end_date">تاريخ الانتهاء</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" required>
                        </div>
                        <div class="form-group">
                            <label for="status">الحالة</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="Pending">قيد الانتظار</option>
                                <option value="Approved">موافق عليها</option>
                                <option value="Rejected">مرفوضة</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#sidebarCollapse').on('click', function () {
                $('#sidebar').toggleClass('active');
            });
        });
    </script>
</body>
</html>