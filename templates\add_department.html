<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة قسم جديد</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f7f6;
            color: #333;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 0;
        }
        .navbar {
            background-color: #2c3e50;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            color: #1abc9c !important;
            font-weight: bold;
            font-size: 1.5em;
        }
        .navbar-nav .nav-link {
            color: #ecf0f1 !important;
            margin: 0 10px;
            transition: color 0.3s ease;
        }
        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: #1abc9c !important;
        }
        .navbar-nav .nav-link i {
            margin-left: 5px;
        }
        .container {
            padding: 30px 15px;
        }
        .form-container {
            max-width: 700px;
            margin: 30px auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .form-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #34495e;
            color: #fff;
            font-size: 1.5em;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-header i {
            margin-left: 10px;
            color: #1abc9c;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-group label {
            font-weight: bold;
            margin-bottom: 8px;
            color: #555;
            display: flex;
            align-items: center;
        }
        .form-group label i {
            margin-left: 8px;
            color: #1abc9c;
            font-size: 1.2em;
        }
        .form-control {
            border-radius: 5px;
            border: 1px solid #ddd;
            padding: 12px;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }
        .form-control:focus {
            border-color: #1abc9c;
            box-shadow: 0 0 0 0.2rem rgba(26, 188, 156, 0.25);
        }
        .btn-primary {
            background-color: #1abc9c;
            border-color: #1abc9c;
            transition: all 0.3s ease;
            padding: 10px 20px;
            font-size: 1.1em;
        }
        .btn-primary:hover {
            background-color: #16a085;
            border-color: #16a085;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: #95a5a6;
            border-color: #95a5a6;
            transition: all 0.3s ease;
            padding: 10px 20px;
            font-size: 1.1em;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
            border-color: #7f8c8d;
            transform: translateY(-2px);
        }
        .btn i {
            margin-left: 5px;
        }
        .action-buttons {
            margin-top: 30px;
            text-align: center;
        }
        .action-buttons .btn {
            margin: 0 5px;
            min-width: 150px;
        }
        .alert {
            margin-bottom: 20px;
            border-radius: 5px;
        }
        @media (max-width: 768px) {
            .form-container {
                padding: 20px;
                margin: 15px auto;
            }
            .card-header {
                font-size: 1.3em;
            }
            .action-buttons .btn {
                width: 100%;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">نظام إدارة الموظفين</a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mr-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/view_employees"><i class="fas fa-users"></i> الموظفون</a>
                    </li>
                    <li class="nav-item active">
                        <a class="nav-link" href="/manage_departments"><i class="fas fa-building"></i> الأقسام</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/logout"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="form-container">
            <div class="card-header">
                <span><i class="fas fa-plus-circle"></i> إضافة قسم جديد</span>
            </div>
            
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <form method="POST" action="{{ url_for('add_department') }}">
                <div class="form-group">
                    <label for="name"><i class="fas fa-tag"></i> اسم القسم:</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="description"><i class="fas fa-align-left"></i> الوصف (اختياري):</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="manager_id"><i class="fas fa-user-tie"></i> مدير القسم (اختياري):</label>
                    <select class="form-control" id="manager_id" name="manager_id">
                        <option value="">-- اختر مدير القسم --</option>
                        {% for employee in employees %}
                        <option value="{{ employee.id }}">{{ employee.full_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="action-buttons">
                    <a href="{{ url_for('manage_departments') }}" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> العودة</a>
                    <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> إضافة القسم</button>
                </div>
            </form>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>