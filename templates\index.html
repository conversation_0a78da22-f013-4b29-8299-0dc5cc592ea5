<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary-color: #4361ee;
            --secondary-color: #3f37c9;
            --accent-color: #4895ef;
            --light-bg: #f8f9fa;
            --text-color: #2b2d42;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        body {
            background: linear-gradient(45deg, #4361ee 0%, #3f37c9 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 20px;
        }
        .welcome-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
            padding: 50px;
            width: 90%;
            max-width: 700px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .welcome-container:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, #4361ee, #4895ef);
        }
        .welcome-container h1 {
            color: #2b2d42;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 25px;
            line-height: 1.3;
        }
        .welcome-container p {
            color: #2b2d42;
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 35px;
            opacity: 0.9;
        }
        .btn-primary {
            background: linear-gradient(45deg, #4361ee, #3f37c9);
            border: none;
            border-radius: 12px;
            padding: 16px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(67, 97, 238, 0.3);
        }
        .btn-primary i {
            font-size: 1.1em;
        }
        
        @media (max-width: 768px) {
            .welcome-container {
                padding: 40px 25px;
            }
            
            .welcome-container h1 {
                font-size: 2rem;
            }
            
            .welcome-container p {
                font-size: 1.1rem;
            }
            
            .btn-primary {
                padding: 14px 30px;
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <h1>أهلاً بك في نظام إدارة الموظفين</h1>
        <p>نظام متكامل لإدارة بيانات الموظفين بكفاءة وفعالية.</p>
        <a href="/login" class="btn btn-primary"><i class="fas fa-sign-in-alt me-2"></i>تسجيل الدخول</a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>