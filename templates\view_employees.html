<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
            color: #333;
        }

        .navbar {
            background-color: #007bff;
            padding: 15px 0;
        }

        .navbar-brand {
            font-weight: bold;
            color: #fff;
        }

        .navbar-nav .nav-link {
            color: #fff;
            margin-right: 15px;
        }

        .navbar-nav .nav-link:hover {
            color: #f8f9fa;
        }

        .container {
            margin-top: 30px;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        h2 {
            color: #007bff;
            margin-bottom: 20px;
            text-align: center;
        }

        .table-responsive {
            margin-top: 20px;
            overflow-x: auto; /* Enable horizontal scrolling for the table */
        }

        .table {
            width: 100%;
            min-width: 1200px; /* Ensure a minimum width for the table to prevent excessive shrinking */
            margin-bottom: 1rem;
            color: #212529;
            border-collapse: collapse;
            font-size: 0.85rem; /* Increased font size for better readability */
            border-radius: 8px; /* Rounded corners for the table */
            overflow: hidden; /* Hide overflow for rounded corners */
        }

        .table th,
        .table td {
            padding: 0.75rem; /* Increased padding for better spacing */
            vertical-align: middle;
            border-top: 1px solid #dee2e6;
            white-space: nowrap; /* Prevent text wrapping in cells */
        }

        .table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
            background-color: #e9ecef;
            font-weight: bold; /* Make header text bold */
            color: #495057; /* Darker color for headers */
            text-align: center; /* Center align header text */
        }

        .table tbody tr:hover {
            background-color: #e2f0ff; /* Lighter blue on hover */
            cursor: pointer;
        }

        .btn {
            padding: 0.35rem 0.7rem; /* Adjusted button padding */
            font-size: 0.85rem; /* Adjusted button font size */
            line-height: 1.5;
            border-radius: 0.25rem;
        }

        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }

        .btn-warning {
            background-color: #ffc107;
            border-color: #ffc107;
        }

        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .btn-success {
            background-color: #28a745;
            border-color: #28a745;
        }

        .action-buttons .btn {
            margin-right: 5px; /* Increased margin between buttons */
        }

        .action-buttons .btn i {
            font-size: 0.9rem; /* Increased icon size */
        }

        .add-employee-btn {
            margin-bottom: 20px;
            text-align: right;
        }

        /* تحسينات جديدة للواجهة */
        .card {
            border: none;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.1);
        }

        .input-group-text {
            background-color: #007bff;
            color: white;
            border: 1px solid #007bff;
        }

        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
            transform: translateY(-2px);
        }

        .highlight {
            background-color: #fff3cd !important;
        }

        /* تحسينات للجدول */
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .table-bordered {
            border: 1px solid #dee2e6;
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid #dee2e6;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .table thead {
                /* display: none; */ /* Removed to keep headers visible */
            }

            .table, .table tbody, .table tr, .table td {
                display: block;
                width: auto;
            }

            .table tr {
                margin-bottom: 0; /* Reset margin */
                border: none; /* Reset border */
            }

            .table td {
                text-align: right;
                padding-left: 0;
                position: relative;
            }

            .table td::before {
                content: attr(data-label);
                position: absolute;
                left: 6px;
                width: 45%;
                padding-right: 10px;
                white-space: nowrap;
                text-align: left;
                font-weight: bold;
            }

            .action-buttons {
                text-align: center;
                margin-top: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        <h2 class="mb-4 text-center">قائمة الموظفين</h2>

        <!-- نموذج البحث المتقدم (بحث من جانب الخادم) -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-primary text-white">
                <i class="fas fa-search me-2"></i> بحث متقدم
            </div>
            <div class="card-body">
                <form action="{{ url_for('view_employees') }}" method="GET" class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="البحث (الاسم، الرقم الوطني، المسمى الوظيفي)" value="{{ search_query }}">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select name="sort_by" class="form-select">
                            <option value="full_name" {% if sort_by == 'full_name' %}selected{% endif %}>ترتيب حسب: الاسم</option>
                            <option value="national_id" {% if sort_by == 'national_id' %}selected{% endif %}>ترتيب حسب: الرقم الوطني</option>
                            <option value="job_title" {% if sort_by == 'job_title' %}selected{% endif %}>ترتيب حسب: المسمى الوظيفي</option>
                            <option value="appointment_date" {% if sort_by == 'appointment_date' %}selected{% endif %}>ترتيب حسب: تاريخ التعيين</option>
                            <option value="years_of_service" {% if sort_by == 'years_of_service' %}selected{% endif %}>ترتيب حسب: سنوات الخدمة</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="sort_order" class="form-select">
                            <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>تصاعدي</option>
                            <option value="desc" {% if sort_order == 'desc' %}selected{% endif %}>تنازلي</option>
                        </select>
                    </div>
                </form>
            </div>
        </div>

        <!-- نموذج البحث السريع (بحث من جانب العميل) -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-info text-white">
                <i class="fas fa-filter me-2"></i> تصفية سريعة
            </div>
            <div class="card-body">
                <form id="searchForm" class="row g-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" id="searchName" class="form-control" placeholder="البحث بالاسم">
                            <span class="input-group-text"><i class="fas fa-user"></i></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" id="searchId" class="form-control" placeholder="البحث بالرقم الوطني">
                            <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" id="searchJob" class="form-control" placeholder="البحث بالمسمى الوظيفي">
                            <span class="input-group-text"><i class="fas fa-briefcase"></i></span>
                        </div>
                    </div>
                    <div class="col-12 text-center">
                        <button type="button" id="searchBtn" class="btn btn-info me-2"><i class="fas fa-search me-2"></i>تصفية</button>
                        <button type="button" id="resetBtn" class="btn btn-secondary"><i class="fas fa-redo me-2"></i>إعادة تعيين</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="d-flex justify-content-between mb-3">
            <div>
                <a href="/add_employee" class="btn btn-primary"><i class="fas fa-user-plus me-2"></i>إضافة موظف جديد</a>
                <button id="printBtn" class="btn btn-success ms-2"><i class="fas fa-print me-2"></i>طباعة القائمة</button>
                <button id="exportBtn" class="btn btn-secondary ms-2"><i class="fas fa-file-excel me-2"></i>تصدير إلى Excel</button>
                <button id="importBtn" class="btn btn-info ms-2"><i class="fas fa-file-import me-2"></i>استيراد من Excel</button>
            </div>
            <a href="/dashboard" class="btn btn-secondary"><i class="fas fa-arrow-circle-left me-2"></i>العودة للوحة التحكم</a>
        </div>

        {% if employees %}
        <!-- إضافة عداد الموظفين -->
        <div class="alert alert-info mb-3 d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-users me-2"></i>
                <span>إجمالي عدد الموظفين: <strong>{{ employees|length }}</strong></span>
            </div>
            <div id="searchResultCount">
                <i class="fas fa-filter me-2"></i>
                <span>نتائج البحث: <strong>{{ employees|length }}</strong></span>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-striped table-bordered align-middle">
                <thead class="table-primary">
                    <tr>
                        <th><i class="fas fa-user me-2"></i>الاسم الكامل</th>
                        <th><i class="fas fa-id-card me-2"></i>الرقم الوطني</th>
                        <th><i class="fas fa-birthday-cake me-2"></i>العمر</th>
                        <th><i class="fas fa-calendar-alt me-2"></i>تاريخ الميلاد</th>
                        <th><i class="fas fa-venus-mars me-2"></i>الجنس</th>
                        <th><i class="fas fa-flag me-2"></i>الجنسية</th>
                        <th><i class="fas fa-hashtag me-2"></i>الرقم الوظيفي</th>
                        <th><i class="fas fa-briefcase me-2"></i>المسمى الوظيفي</th>
                        <th><i class="fas fa-file-contract me-2"></i>نوع التعيين</th>
                        <th><i class="fas fa-calendar-check me-2"></i>تاريخ التعيين</th>
                        <th><i class="fas fa-play me-2"></i>تاريخ المباشرة</th>
                        <th><i class="fas fa-level-up-alt me-2"></i>الدرجة الحالية</th>
                        <th><i class="fas fa-plus-circle me-2"></i>عدد العلاوات</th>
                        <th><i class="fas fa-arrow-up me-2"></i>تاريخ آخر ترقية</th>
                        <th><i class="fas fa-history me-2"></i>سنوات الخدمة</th>
                        <th><i class="fas fa-cogs me-2"></i>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td data-label="الاسم الكامل">{{ employee.full_name }}</td>
                        <td data-label="الرقم الوطني">{{ employee.national_id }}</td>
                        <td data-label="العمر">{{ employee.age }}</td>
                        <td data-label="تاريخ الميلاد">{{ employee.date_of_birth }}</td>
                        <td data-label="الجنس">{{ employee.gender }}</td>
                        <td data-label="الجنسية">{{ employee.nationality }}</td>
                        <td data-label="الرقم الوظيفي">{{ employee.employee_number }}</td>
                        <td data-label="المسمى الوظيفي">{{ employee.job_title }}</td>
                        <td data-label="نوع التعيين">{{ employee.appointment_type }}</td>
                        <td data-label="تاريخ التعيين">{{ employee.appointment_date }}</td>
                        <td data-label="تاريخ المباشرة">{{ employee.start_date }}</td>
                        <td data-label="الدرجة الحالية">{{ employee.current_grade }}</td>
                        <td data-label="عدد العلاوات">{{ employee.num_increments }}</td>
                        <td data-label="تاريخ آخر ترقية">{{ employee.last_promotion_date }}</td>
                        <td data-label="سنوات الخدمة">{{ employee.years_of_service }}</td>
                        <td data-label="الإجراءات" class="text-center">
                            <div class="btn-group">
                                <a href="{{ url_for('edit_employee', employee_id=employee.id) }}" class="btn btn-warning btn-sm me-1" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('employee_details', employee_id=employee.id) }}" class="btn btn-info btn-sm me-1" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <form action="{{ url_for('delete_employee', employee_id=employee.id) }}" method="POST" style="display:inline;">
                                    <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد أنك تريد حذف هذا الموظف؟');" title="حذف">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
            <p class="text-center">لا يوجد موظفون مسجلون بعد.</p>
        {% endif %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- إضافة كود JavaScript للبحث -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // الحصول على عناصر البحث
            const searchNameInput = document.getElementById('searchName');
            const searchIdInput = document.getElementById('searchId');
            const searchJobInput = document.getElementById('searchJob');
            const searchBtn = document.getElementById('searchBtn');
            const resetBtn = document.getElementById('resetBtn');
            const tableRows = document.querySelectorAll('table tbody tr');
            const searchResultCount = document.getElementById('searchResultCount').querySelector('strong');
            const printBtn = document.getElementById('printBtn');
            const exportBtn = document.getElementById('exportBtn');
            const importBtn = document.getElementById('importBtn');
            
            // وظيفة البحث
            function performSearch() {
                const nameQuery = searchNameInput.value.toLowerCase();
                const idQuery = searchIdInput.value.toLowerCase();
                const jobQuery = searchJobInput.value.toLowerCase();
                
                // إزالة التظليل من جميع الصفوف
                tableRows.forEach(row => {
                    row.classList.remove('highlight');
                });
                
                let visibleRowsCount = 0;

            // وظيفة زر الاستيراد من Excel
            importBtn.addEventListener('click', function() {
                alert('سيتم تنفيذ وظيفة استيراد Excel هنا!');
                // هنا يمكنك إضافة منطق استيراد ملف Excel
            });
                
                // البحث في كل صف
                tableRows.forEach(row => {
                    const nameCell = row.querySelector('td[data-label="الاسم الكامل"]').textContent.toLowerCase();
                    const idCell = row.querySelector('td[data-label="الرقم الوطني"]').textContent.toLowerCase();
                    const jobCell = row.querySelector('td[data-label="المسمى الوظيفي"]').textContent.toLowerCase();
                    
                    // التحقق من تطابق معايير البحث
                    const nameMatch = nameQuery === '' || nameCell.includes(nameQuery);
                    const idMatch = idQuery === '' || idCell.includes(idQuery);
                    const jobMatch = jobQuery === '' || jobCell.includes(jobQuery);
                    
                    // إظهار أو إخفاء الصف بناءً على نتائج البحث
                    if (nameMatch && idMatch && jobMatch) {
                        row.style.display = '';
                        visibleRowsCount++;
                        // تظليل الصفوف المطابقة إذا تم إدخال معايير بحث
                        if (nameQuery !== '' || idQuery !== '' || jobQuery !== '') {
                            row.classList.add('highlight');
                        }
                    } else {
                        row.style.display = 'none';
                    }
                });
                
                // تحديث عداد نتائج البحث
                searchResultCount.textContent = visibleRowsCount;
                
                // إظهار رسالة إذا لم يتم العثور على نتائج
                const noResultsMessage = document.getElementById('noResultsMessage');
                if (visibleRowsCount === 0) {
                    if (!noResultsMessage) {
                        const message = document.createElement('div');
                        message.id = 'noResultsMessage';
                        message.className = 'alert alert-warning mt-3 text-center';
                        message.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i> لم يتم العثور على نتائج مطابقة لمعايير البحث.';
                        document.querySelector('.table-responsive').before(message);
                    }
                } else if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }
            
            // وظيفة إعادة تعيين البحث
            function resetSearch() {
                searchNameInput.value = '';
                searchIdInput.value = '';
                searchJobInput.value = '';
                
                // إظهار جميع الصفوف وإزالة التظليل
                tableRows.forEach(row => {
                    row.style.display = '';
                    row.classList.remove('highlight');
                });
                
                // إعادة تعيين عداد نتائج البحث
                searchResultCount.textContent = tableRows.length;
                
                // إزالة رسالة عدم وجود نتائج إذا كانت موجودة
                const noResultsMessage = document.getElementById('noResultsMessage');
                if (noResultsMessage) {
                    noResultsMessage.remove();
                }
            }
            
            // وظيفة طباعة القائمة
            function printEmployeeList() {
                // إنشاء نافذة طباعة جديدة
                const printWindow = window.open('', '_blank');
                
                // إنشاء محتوى HTML للطباعة
                let printContent = `
                    <!DOCTYPE html>
                    <html lang="ar" dir="rtl">
                    <head>
                        <meta charset="UTF-8">
                        <title>قائمة الموظفين</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
                        <style>
                            body { font-family: Arial, sans-serif; }
                            h2 { text-align: center; margin-bottom: 20px; }
                            table { width: 100%; border-collapse: collapse; }
                            th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                            th { background-color: #f2f2f2; }
                            .print-header { text-align: center; margin-bottom: 20px; }
                            .print-date { text-align: left; margin-bottom: 10px; }
                        </style>
                    </head>
                    <body>
                        <div class="print-header">
                            <h2>قائمة الموظفين</h2>
                            <p>إجمالي عدد الموظفين: ${tableRows.length}</p>
                        </div>
                        <div class="print-date">
                            <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th>الاسم الكامل</th>
                                    <th>الرقم الوطني</th>
                                    <th>المسمى الوظيفي</th>
                                    <th>تاريخ التعيين</th>
                                    <th>سنوات الخدمة</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                // إضافة صفوف الجدول المرئية فقط
                tableRows.forEach(row => {
                    if (row.style.display !== 'none') {
                        const name = row.querySelector('td[data-label="الاسم الكامل"]').textContent;
                        const id = row.querySelector('td[data-label="الرقم الوطني"]').textContent;
                        const job = row.querySelector('td[data-label="المسمى الوظيفي"]').textContent;
                        const appointmentDate = row.querySelector('td[data-label="تاريخ التعيين"]').textContent;
                        const yearsOfService = row.querySelector('td[data-label="سنوات الخدمة"]').textContent;
                        
                        printContent += `
                            <tr>
                                <td>${name}</td>
                                <td>${id}</td>
                                <td>${job}</td>
                                <td>${appointmentDate}</td>
                                <td>${yearsOfService}</td>
                            </tr>
                        `;
                    }
                });
                
                printContent += `
                            </tbody>
                        </table>
                    </body>
                    </html>
                `;
                
                // كتابة المحتوى إلى نافذة الطباعة
                printWindow.document.open();
                printWindow.document.write(printContent);
                printWindow.document.close();
                
                // انتظار تحميل الصفحة ثم طباعتها
                printWindow.onload = function() {
                    printWindow.print();
                };
            }
            
            // وظيفة تصدير البيانات إلى Excel
            function exportToExcel() {
                // إنشاء مصفوفة لتخزين البيانات
                const data = [];
                
                // إضافة عناوين الأعمدة
                const headers = [
                    'الاسم الكامل', 'الرقم الوطني', 'العمر', 'تاريخ الميلاد',
                    'الجنس', 'الجنسية', 'الرقم الوظيفي', 'المسمى الوظيفي',
                    'نوع التعيين', 'تاريخ التعيين', 'تاريخ بدء العمل',
                    'الدرجة الحالية', 'عدد العلاوات', 'تاريخ آخر ترقية', 'سنوات الخدمة'
                ];
                data.push(headers);
                
                // إضافة بيانات الصفوف المرئية فقط
                tableRows.forEach(row => {
                    if (row.style.display !== 'none') {
                        const rowData = [];
                        row.querySelectorAll('td').forEach((cell, index) => {
                            // تجاهل عمود الإجراءات
                            if (index < 15) {
                                rowData.push(cell.textContent.trim());
                            }
                        });
                        data.push(rowData);
                    }
                });
                
                // تحويل البيانات إلى CSV
                let csvContent = '';
                data.forEach(rowArray => {
                    const row = rowArray.join(',');
                    csvContent += row + '\r\n';
                });
                
                // إنشاء رابط تنزيل
                const encodedUri = encodeURI('data:text/csv;charset=utf-8,' + csvContent);
                const link = document.createElement('a');
                link.setAttribute('href', encodedUri);
                link.setAttribute('download', `قائمة_الموظفين_${new Date().toLocaleDateString('ar-SA')}.csv`);
                document.body.appendChild(link);
                
                // تنزيل الملف
                link.click();
                document.body.removeChild(link);
            }
            
            // إضافة مستمعي الأحداث
            searchBtn.addEventListener('click', performSearch);
            resetBtn.addEventListener('click', resetSearch);
            printBtn.addEventListener('click', printEmployeeList);
            exportBtn.addEventListener('click', exportToExcel);
            
            // البحث أثناء الكتابة (اختياري)
            searchNameInput.addEventListener('input', performSearch);
            searchIdInput.addEventListener('input', performSearch);
            searchJobInput.addEventListener('input', performSearch);
            
            // إضافة تأثير تلميح للأزرار
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(button => {
                if (button.title) {
                    button.setAttribute('data-bs-toggle', 'tooltip');
                    button.setAttribute('data-bs-placement', 'top');
                }
            });
            
            // تفعيل التلميحات
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
</body>
</html>