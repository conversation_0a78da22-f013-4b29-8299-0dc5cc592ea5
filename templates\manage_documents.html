<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الوثائق</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            background-color: #343a40 !important;
        }
        .sidebar {
            height: 100vh;
            background-color: #343a40;
            padding-top: 20px;
            color: white;
        }
        .sidebar .nav-link {
            color: white;
            padding: 10px 15px;
            transition: background-color 0.3s;
        }
        .sidebar .nav-link:hover {
            background-color: #495057;
        }
        .sidebar .nav-link.active {
            background-color: #007bff;
        }
        .content {
            padding: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .table-responsive {
            margin-top: 20px;
        }
        .table th,
        .table td {
            white-space: nowrap; /* Prevent text wrapping in table cells */
            font-size: 0.95rem;
            padding: 0.75rem;
        }
        .table thead th {
            background-color: #343a40;
            color: white;
            border-bottom: none;
        }
        .table-hover tbody tr:hover {
            background-color: #e2e6ea;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }
        @media (max-width: 768px) {
            .sidebar {
                height: auto;
            }
            .content {
                padding-top: 0;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">نظام شؤون الموظفين</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_employees') }}"><i class="fas fa-users"></i> الموظفون</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('manage_departments') }}"><i class="fas fa-building"></i> الأقسام</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('manage_attendance') }}"><i class="fas fa-clock"></i> الحضور</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('manage_departure') }}"><i class="fas fa-sign-out-alt"></i> الانصراف</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ url_for('manage_documents') }}"><i class="fas fa-file-alt"></i> الوثائق</a>
                    </li>
                </ul>
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">
                                <i class="fas fa-home"></i>
                                الرئيسية
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_employees') }}">
                                <i class="fas fa-users"></i>
                                الموظفون
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_departments') }}">
                                <i class="fas fa-building"></i>
                                الأقسام
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_attendance') }}">
                                <i class="fas fa-clock"></i>
                                الحضور
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_departure') }}">
                                <i class="fas fa-sign-out-alt"></i>
                                الانصراف
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ url_for('manage_documents') }}">
                                <i class="fas fa-file-alt"></i>
                                الوثائق
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 content">
<div class="card-header">
                إدارة المستندات
                <a href="{{ url_for('add_document') }}" class="btn btn-primary btn-sm"><i class="fas fa-plus"></i> إضافة مستند جديد</a>
            </div>

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="card mb-4">
                    <div class="card-header">بحث وتصفية الوثائق</div>
                    <div class="card-body">
                        <form method="GET" action="{{ url_for('manage_documents') }}">
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <label for="search" class="form-label">بحث بالاسم:</label>
                                    <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="اسم الموظف">
                                </div>
                                <div class="col-md-4">
                                    <label for="employee_id" class="form-label">تصفية حسب الموظف:</label>
                                    <select class="form-select" id="employee_id" name="employee_id">
                                        <option value="">جميع الموظفين</option>
                                        {% for employee in all_employees %}
                                            <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>{{ employee.full_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="document_type" class="form-label">تصفية حسب نوع الوثيقة:</label>
                                    <input type="text" class="form-control" id="document_type" name="document_type" value="{{ filter_document_type }}" placeholder="نوع الوثيقة">
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary"><i class="fas fa-search"></i> بحث وتصفية</button>
                                    <a href="{{ url_for('manage_documents') }}" class="btn btn-secondary"><i class="fas fa-sync"></i> مسح التصفية</a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

<div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع المستند</th>
                                    <th>اسم الملف</th>
                                    <th>تاريخ الرفع</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for document in documents %}
                                <tr>
                                    <td>{{ document.employee.full_name }}</td>
                                    <td>{{ document.document_type }}</td>
                                    <td>{{ document.file_name }}</td>
                                    <td>{{ document.upload_date.strftime('%Y-%m-%d') }}</td>
                                    <td class="action-icons">
                                        <a href="{{ url_for('uploaded_file', filename=document.file_name) }}" class="btn btn-info btn-sm" target="_blank" title="عرض المستند"><i class="fas fa-eye"></i></a>
                                        <a href="{{ url_for('edit_document', document_id=document.id) }}" class="btn btn-primary btn-sm" title="تعديل"><i class="fas fa-edit"></i></a>
                                        <a href="{{ url_for('delete_document', document_id=document.id) }}" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من حذف هذا المستند؟');" title="حذف"><i class="fas fa-trash"></i></a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS and Popper.js -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>