<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة سجل حضور</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }
        .wrapper {
            display: flex;
            width: 100%;
            align-items: stretch;
        }
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            background: #343a40;
            color: #fff;
            transition: all 0.3s;
        }
        #sidebar.active {
            margin-right: -250px;
        }
        #sidebar .sidebar-header {
            padding: 20px;
            background: #343a40;
        }
        #sidebar ul.components {
            padding: 20px 0;
            border-bottom: 1px solid #47748b;
        }
        #sidebar ul p {
            color: #fff;
            padding: 10px;
        }
        #sidebar ul li a {
            padding: 10px;
            font-size: 1.1em;
            display: block;
            color: #fff;
        }
        #sidebar ul li a:hover {
            color: #343a40;
            background: #fff;
        }
        #content {
            width: 100%;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s;
        }
        .navbar {
            margin-bottom: 20px;
        }
        .card {
            margin-bottom: 20px;
            border: none;
            border-radius: 0.5rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #007bff;
            color: white;
            border-bottom: none;
            border-top-left-radius: 0.5rem;
            border-top-right-radius: 0.5rem;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <h3>نظام إدارة الموظفين</h3>
            </div>
            <ul class="list-unstyled components">
                <li><a href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> لوحة التحكم</a></li>
                <li><a href="{{ url_for('view_employees') }}"><i class="fas fa-users"></i> إدارة الموظفين</a></li>
                <li><a href="{{ url_for('manage_departments') }}"><i class="fas fa-building"></i> إدارة الأقسام</a></li>
                <li class="active"><a href="{{ url_for('manage_attendance') }}"><i class="fas fa-check-circle"></i> إدارة الحضور</a></li>
                <li><a href="#"><i class="fas fa-file-alt"></i> التقارير</a></li>
                <li><a href="#"><i class="fas fa-cog"></i> الإعدادات</a></li>
                <li><a href="{{ url_for('login') }}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a></li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-align-right"></i>
                        <span>تبديل الشريط الجانبي</span>
                    </button>
                    <div class="ml-auto">
                        <h5>مرحباً بك، المستخدم!</h5>
                    </div>
                </div>
            </nav>

            <div class="container-fluid">
                <div class="card">
                    <div class="card-header">
                        إضافة سجل حضور جديد
                    </div>
                    <div class="card-body">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                        {{ message }}
                                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        <form method="POST" action="{{ url_for('add_attendance') }}">
                            <div class="form-group">
                                <label for="employee_id">الموظف:</label>
                                <select class="form-control" id="employee_id" name="employee_id" required>
                                    <option value="">اختر موظفاً</option>
                                    {% for employee in employees %}
                                        <option value="{{ employee.id }}">{{ employee.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="date">التاريخ:</label>
                                <input type="date" class="form-control" id="date" name="date" required>
                            </div>
                            <div class="form-group">
                                <label for="status">الحالة:</label>
                                <select class="form-control" id="status" name="status" required>
                                    <option value="حاضر">حاضر</option>
                                    <option value="غائب">غائب</option>
                                    <option value="إجازة">إجازة</option>
                                    <option value="مهمة رسمية">مهمة رسمية</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary"><i class="fas fa-plus"></i> إضافة سجل الحضور</button>
                            <a href="{{ url_for('manage_attendance') }}" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</a>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#sidebarCollapse').on('click', function () {
                $('#sidebar').toggleClass('active');
            });
        });
    </script>
</body>
</html>