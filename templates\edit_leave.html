<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل الإجازة</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .wrapper {
            display: flex;
            width: 100%;
            align-items: stretch;
        }
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            background: #343a40;
            color: #fff;
            transition: all 0.3s;
        }
        #sidebar.active {
            margin-right: -250px;
        }
        #sidebar .sidebar-header {
            padding: 20px;
            background: #343a40;
        }
        #sidebar ul.components {
            padding: 20px 0;
            border-bottom: 1px solid #47748b;
        }
        #sidebar ul p {
            color: #fff;
            padding: 10px;
        }
        #sidebar ul li a {
            padding: 10px;
            font-size: 1.1em;
            display: block;
            color: #fff;
        }
        #sidebar ul li a:hover {
            color: #343a40;
            background: #fff;
        }
        #content {
            width: 100%;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s;
        }
        .card {
            margin-bottom: 20px;
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.05);
        }
        .card-header {
            background-color: #007bff;
            color: white;
            border-bottom: none;
            border-radius: 10px 10px 0 0;
        }
        .table th,
        .table td {
            text-align: right;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
        .form-control:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header text-center">
                <h3>نظام إدارة الموظفين</h3>
            </div>
            <ul class="list-unstyled components">
                <li>
                    <a href="{{ url_for('dashboard') }}">
                        <i class="fas fa-home"></i>
                        الرئيسية
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('view_employees') }}">
                        <i class="fas fa-users"></i>
                        الموظفون
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departments') }}">
                        <i class="fas fa-building"></i>
                        الأقسام
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('manage_attendance') }}">
                        <i class="fas fa-check-circle"></i>
                        الحضور والانصراف
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departure') }}">
                        <i class="fas fa-plane-departure"></i>
                        المغادرات
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('manage_documents') }}">
                        <i class="fas fa-file-alt"></i>
                        المستندات
                    </a>
                </li>
                <li class="active">
                    <a href="{{ url_for('manage_leaves') }}">
                        <i class="fas fa-calendar-alt"></i>
                        الإجازات
                    </a>
                </li>
                <li>
                    <a href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
                <button type="button" id="sidebarCollapse" class="btn btn-info">
                    <i class="fas fa-align-right"></i>
                    <span>تبديل الشريط الجانبي</span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav mr-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard') }}">الرئيسية</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('view_employees') }}">الموظفون</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_departments') }}">الأقسام</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_attendance') }}">الحضور والانصراف</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_departure') }}">المغادرات</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('manage_documents') }}">المستندات</a>
                        </li>
                        <li class="nav-item active">
                            <a class="nav-link" href="{{ url_for('manage_leaves') }}">الإجازات <span class="sr-only">(الحالي)</span></a>
                        </li>
                    </ul>
                </div>
            </nav>

            <h2 class="mb-4">تعديل الإجازة</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="card">
                <div class="card-header">
                    تعديل بيانات الإجازة
                </div>
                <div class="card-body">
                    <form action="{{ url_for('edit_leave', leave_id=leave.id) }}" method="POST">
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="employee_id">الموظف</label>
                                <select class="form-control" id="employee_id" name="employee_id" required>
                                    {% for employee in employees %}
                                    <option value="{{ employee.id }}" {% if employee.id == leave.employee_id %}selected{% endif %}>{{ employee.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="leave_type">نوع الإجازة</label>
                                <select class="form-control" id="leave_type" name="leave_type" required>
                                    <option value="">-- اختر نوع الإجازة --</option>
                                    <option value="سنوية" {% if leave.leave_type == 'سنوية' %}selected{% endif %}>سنوية</option>
                                    <option value="مرضية" {% if leave.leave_type == 'مرضية' %}selected{% endif %}>مرضية</option>
                                    <option value="عارضة" {% if leave.leave_type == 'عارضة' %}selected{% endif %}>عارضة</option>
                                    <option value="بدون راتب" {% if leave.leave_type == 'بدون راتب' %}selected{% endif %}>بدون راتب</option>
                                    <option value="أخرى" {% if leave.leave_type == 'أخرى' %}selected{% endif %}>أخرى</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group col-md-6">
                                <label for="start_date">تاريخ البدء</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ leave.start_date.strftime('%Y-%m-%d') }}" required>
                            </div>
                            <div class="form-group col-md-6">
                                <label for="end_date">تاريخ الانتهاء</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ leave.end_date.strftime('%Y-%m-%d') }}" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="status">الحالة</label>
                            <select class="form-control" id="status" name="status" required>
                                <option value="Pending" {% if leave.status == 'Pending' %}selected{% endif %}>قيد الانتظار</option>
                                <option value="Approved" {% if leave.status == 'Approved' %}selected{% endif %}>موافق عليها</option>
                                <option value="Rejected" {% if leave.status == 'Rejected' %}selected{% endif %}>مرفوضة</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> تعديل الإجازة</button>
                        <a href="{{ url_for('manage_leaves') }}" class="btn btn-secondary"><i class="fas fa-times"></i> إلغاء</a>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#sidebarCollapse').on('click', function () {
                $('#sidebar').toggleClass('active');
            });
        });
    </script>
</body>
</html>