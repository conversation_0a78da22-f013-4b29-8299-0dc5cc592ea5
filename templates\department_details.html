<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل القسم</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f4f7f6;
            color: #333;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 0;
        }
        .navbar {
            background-color: #2c3e50;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            color: #1abc9c !important;
            font-weight: bold;
            font-size: 1.5em;
        }
        .navbar-nav .nav-link {
            color: #ecf0f1 !important;
            margin: 0 10px;
            transition: color 0.3s ease;
        }
        .navbar-nav .nav-link:hover,
        .navbar-nav .nav-link.active {
            color: #1abc9c !important;
        }
        .navbar-nav .nav-link i {
            margin-left: 5px;
        }
        .container {
            padding: 30px 15px;
        }
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
            border: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            background-color: #34495e;
            color: #fff;
            font-size: 1.5em;
            font-weight: bold;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .card-body {
            padding: 25px;
        }
        .detail-item {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .detail-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
        }
        .detail-label i {
            margin-left: 8px;
            color: #1abc9c;
            font-size: 1.2em;
        }
        .detail-value {
            font-size: 1.1em;
            color: #333;
            padding-right: 25px;
        }
        .btn-primary {
            background-color: #1abc9c;
            border-color: #1abc9c;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #16a085;
            border-color: #16a085;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: #95a5a6;
            border-color: #95a5a6;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
            border-color: #7f8c8d;
            transform: translateY(-2px);
        }
        .btn i {
            margin-left: 5px;
        }
        .department-stats {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 10px;
            background-color: #f8f9fa;
            flex: 1;
            margin: 0 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .stat-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .stat-item i {
            font-size: 2em;
            margin-bottom: 10px;
            color: #1abc9c;
        }
        .stat-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #34495e;
        }
        .stat-label {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .action-buttons {
            margin-top: 20px;
            text-align: center;
        }
        .action-buttons .btn {
            margin: 0 5px;
            padding: 8px 20px;
        }
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            .card-header {
                font-size: 1.3em;
            }
            .department-stats {
                flex-direction: column;
            }
            .stat-item {
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <a class="navbar-brand" href="#">نظام إدارة الموظفين</a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav ml-auto">
                <li class="nav-item">
                    <a class="nav-link" href="/dashboard">الرئيسية <i class="fas fa-home"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/view_employees">الموظفون <i class="fas fa-users"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/manage_departments">الأقسام <i class="fas fa-building"></i></a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="/logout">تسجيل الخروج <i class="fas fa-sign-out-alt"></i></a>
                </li>
            </ul>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="card">
                    <div class="card-header">
                        <span><i class="fas fa-building"></i> تفاصيل القسم: {{ department.name }}</span>
                        <div>
                            <a href="{{ url_for('edit_department', department_id=department.id) }}" class="btn btn-sm btn-primary"><i class="fas fa-edit"></i> تعديل</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="department-stats">
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <div class="stat-value">{{ employee_count }}</div>
                                <div class="stat-label">عدد الموظفين</div>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-user-tie"></i>
                                <div class="stat-value">{{ manager_name }}</div>
                                <div class="stat-label">مدير القسم</div>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-calendar-alt"></i>
                                <div class="stat-value">{{ department.creation_date.strftime('%Y-%m-%d') }}</div>
                                <div class="stat-label">تاريخ الإنشاء</div>
                            </div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-tag"></i> اسم القسم:</div>
                            <div class="detail-value">{{ department.name }}</div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-align-left"></i> الوصف:</div>
                            <div class="detail-value">{{ department.description or 'لا يوجد وصف' }}</div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-calendar-alt"></i> تاريخ الإنشاء:</div>
                            <div class="detail-value">{{ department.creation_date.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-users"></i> عدد الموظفين:</div>
                            <div class="detail-value">{{ employee_count }}</div>
                        </div>
                        
                        <div class="detail-item">
                            <div class="detail-label"><i class="fas fa-user-tie"></i> مدير القسم:</div>
                            <div class="detail-value">{{ manager_name }}</div>
                        </div>
                        
                        <div class="action-buttons">
                            <a href="{{ url_for('manage_departments') }}" class="btn btn-secondary"><i class="fas fa-arrow-right"></i> العودة إلى الأقسام</a>
                            <a href="{{ url_for('edit_department', department_id=department.id) }}" class="btn btn-primary"><i class="fas fa-edit"></i> تعديل القسم</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.2/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
</body>
</html>