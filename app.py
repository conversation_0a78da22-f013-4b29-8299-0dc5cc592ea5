from flask import Flask, render_template, request, redirect, url_for, flash, get_flashed_messages, send_from_directory, session, send_file
from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta # أضف هذا السطر
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
import os
import pandas as pd
import tempfile
from collections import defaultdict
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_secret_key_here' # يجب تغيير هذا المفتاح في بيئة الإنتاج
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///employees.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
db = SQLAlchemy(app)
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'gif'} # يمكنك تعديل الامتدادات المسموح بها
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# التأكد من وجود مجلد التحميل
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# تعريف نموذج المستخدم لـ Flask-Login
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password = db.Column(db.String(120), nullable=False)

    def __repr__(self):
        return f'<User {self.username}>'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# تعريف نموذج الموظف
class Employee(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    # البيانات الشخصية
    full_name = db.Column(db.String(100), nullable=False)
    national_id = db.Column(db.String(20), unique=True, nullable=False)
    age = db.Column(db.Integer)
    birth_place_city = db.Column(db.String(50))
    birth_place_region = db.Column(db.String(50))
    birth_place_municipality = db.Column(db.String(50))
    date_of_birth = db.Column(db.String(20))
    gender = db.Column(db.String(10))
    nationality = db.Column(db.String(50))

    # البيانات الوظيفية
    employee_id = db.Column(db.String(20), unique=True, nullable=False)
    job_title = db.Column(db.String(100))
    appointment_type = db.Column(db.String(50)) # تعيين-عقد-ندب-تكليف
    appointment_date = db.Column(db.String(20))
    start_work_date = db.Column(db.String(20))
    current_grade = db.Column(db.String(20))
    allowances_count = db.Column(db.Integer)
    last_promotion_date = db.Column(db.String(20))
    years_of_service = db.Column(db.Integer)
    photo = db.Column(db.String(100)) # مسار الصورة
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'), nullable=True) # إضافة حقل department_id
    department = db.relationship('Department', backref='employees', foreign_keys=[department_id]) # علاقة عكسية لجلب الموظفين في القسم

    def __repr__(self):
        return f'<Employee {self.full_name}>'


# تعريف نموذج القسم
class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), unique=True, nullable=False)
    description = db.Column(db.String(255))
    creation_date = db.Column(db.DateTime, default=datetime.utcnow) # إضافة تاريخ الإنشاء
    manager_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=True) # إضافة مدير القسم
    manager = db.relationship('Employee', foreign_keys=[manager_id]) # علاقة لمدير القسم

    def __repr__(self):
        return f'<Department {self.name}>'

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        # هنا يمكنك إضافة منطق التحقق من قاعدة البيانات
        if username == 'admin' and password == 'password': # مثال بسيط
            return redirect(url_for('dashboard')) # إعادة توجيه إلى لوحة التحكم
        else:
            return render_template('login.html', error='اسم المستخدم أو كلمة المرور غير صحيحة')
    return render_template('login.html')

@app.route('/logout')
def logout():
    session.pop('username', None) # يمكنك تعديل هذا بناءً على كيفية تخزين معلومات المستخدم في الجلسة
    flash('تم تسجيل الخروج بنجاح!', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html')

@app.route('/add_employee', methods=['GET', 'POST'])
def add_employee():
    if request.method == 'POST':
        try:
            new_employee = Employee(
                full_name=request.form['full_name'],
                national_id=request.form['national_id'],
                age=request.form.get('age', type=int),
                birth_place_city=request.form.get('birth_place_city'),
                birth_place_region=request.form.get('birth_place_region'),
                birth_place_municipality=request.form.get('birth_place_municipality'),
                date_of_birth=request.form.get('date_of_birth'),
                gender=request.form.get('gender'),
                nationality=request.form.get('nationality'),
                employee_id=request.form['employee_id'],
                job_title=request.form.get('job_title'),
                appointment_type=request.form.get('appointment_type'),
                appointment_date=request.form.get('appointment_date'),
                start_work_date=request.form.get('start_work_date'),
                current_grade=request.form.get('current_grade'),
                allowances_count=request.form.get('allowances_count', type=int),
                last_promotion_date=request.form.get('last_promotion_date'),
                years_of_service=request.form.get('years_of_service', type=int),
                photo=request.form.get('photo')
            )
            db.session.add(new_employee)
            db.session.commit()
            flash('تمت إضافة الموظف بنجاح!', 'success')
            return redirect(url_for('view_employees')) # Redirect to view_employees after adding
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة الموظف: {e}', 'danger')
            return redirect(url_for('add_employee')) # Redirect back to add_employee form
    return render_template('add_employee.html')

@app.route('/view_employees')
def view_employees():
    # الحصول على معلمات البحث من الاستعلام
    search_query = request.args.get('search', '')
    sort_by = request.args.get('sort_by', 'full_name')
    sort_order = request.args.get('sort_order', 'asc')
    
    # البدء بجميع الموظفين
    query = Employee.query
    
    # تطبيق البحث إذا تم تقديم استعلام
    if search_query:
        query = query.filter(
            db.or_(
                Employee.full_name.ilike(f'%{search_query}%'),
                Employee.national_id.ilike(f'%{search_query}%'),
                Employee.job_title.ilike(f'%{search_query}%')
            )
        )
    
    # تطبيق الترتيب
    if sort_order == 'asc':
        query = query.order_by(getattr(Employee, sort_by).asc())
    else:
        query = query.order_by(getattr(Employee, sort_by).desc())
    
    # الحصول على النتائج
    employees = query.all()
    
    return render_template('view_employees.html', employees=employees, 
                           search_query=search_query, sort_by=sort_by, sort_order=sort_order)

@app.route('/edit_employee/<int:employee_id>', methods=['GET', 'POST'])
def edit_employee(employee_id):
    employee = Employee.query.get_or_404(employee_id)
    if request.method == 'POST':
        employee.full_name = request.form['full_name']
        employee.national_id = request.form['national_id']
        employee.age = request.form.get('age', type=int) # Added type=int
        employee.birth_place_city = request.form['birth_place_city']
        employee.birth_place_region = request.form['birth_place_region']
        employee.birth_place_municipality = request.form['birth_place_municipality']
        employee.date_of_birth = request.form['date_of_birth']
        employee.gender = request.form['gender']
        employee.nationality = request.form['nationality']
        employee.employee_id = request.form['employee_id']
        employee.job_title = request.form['job_title']
        employee.appointment_type = request.form['appointment_type']
        employee.appointment_date = request.form['appointment_date']
        employee.start_work_date = request.form['start_work_date']
        employee.current_grade = request.form['current_grade']
        employee.allowances_count = request.form.get('allowances_count', type=int) # Added type=int
        employee.last_promotion_date = request.form['last_promotion_date']
        employee.years_of_service = request.form.get('years_of_service', type=int) # Added type=int
        try:
            db.session.commit()
            flash('تم تحديث بيانات الموظف بنجاح!', 'success')
            return redirect(url_for('view_employees'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث بيانات الموظف: {e}', 'danger')
    return render_template('edit_employee.html', employee=employee)

@app.route('/delete_employee/<int:employee_id>', methods=['POST'])
def delete_employee(employee_id):
    employee = Employee.query.get_or_404(employee_id)
    try:
        db.session.delete(employee)
        db.session.commit()
        flash('تم حذف الموظف بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الموظف: {e}', 'danger')
    return redirect(url_for('view_employees'))

@app.route('/employee_details/<int:employee_id>')
def employee_details(employee_id):
    employee = Employee.query.get_or_404(employee_id)
    documents = Document.query.filter_by(employee_id=employee_id).all()
    return render_template('employee_details.html', employee=employee, documents=documents)

@app.route('/view_department/<int:department_id>')
def view_department(department_id):
    department = Department.query.get_or_404(department_id)
    employee_count = len(department.employees)
    manager_name = department.manager.full_name if department.manager else 'لا يوجد مدير'
    return render_template('department_details.html', department=department, employee_count=employee_count, manager_name=manager_name)

@app.route('/edit_department/<int:department_id>', methods=['GET', 'POST'])
def edit_department(department_id):
    department = Department.query.get_or_404(department_id)
    employees = Employee.query.all() # لجلب قائمة الموظفين لاختيار المدير
    if request.method == 'POST':
        department.name = request.form['name']
        department.description = request.form.get('description')
        manager_id = request.form.get('manager_id')
        if manager_id and manager_id != 'None':
            department.manager_id = int(manager_id)
        else:
            department.manager_id = None
        try:
            db.session.commit()
            flash('تم تحديث بيانات القسم بنجاح!', 'success')
            return redirect(url_for('manage_departments'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث بيانات القسم: {e}', 'danger')
    return render_template('edit_department.html', department=department, employees=employees)

@app.route('/delete_department/<int:department_id>', methods=['POST'])
def delete_department(department_id):
    department = Department.query.get_or_404(department_id)
    try:
        db.session.delete(department)
        db.session.commit()
        flash('تم حذف القسم بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف القسم: {e}', 'danger')
    return redirect(url_for('manage_departments'))

@app.route('/manage_departments')
def manage_departments():
    search_query = request.args.get('search', '')
    filter_manager_id = request.args.get('manager_id', type=int)

    departments_query = Department.query

    if search_query:
        departments_query = departments_query.filter(
            (Department.name.ilike(f'%{search_query}%')) |
            (Department.description.ilike(f'%{search_query}%'))
        )

    if filter_manager_id:
        departments_query = departments_query.filter_by(manager_id=filter_manager_id)

    departments = departments_query.all()

    departments_data = []
    for department in departments:
        employee_count = len(department.employees)
        manager_name = department.manager.full_name if department.manager else 'لا يوجد مدير'
        departments_data.append({
            'id': department.id,
            'name': department.name,
            'description': department.description,
            'creation_date': department.creation_date.strftime('%Y-%m-%d %H:%M:%S'),
            'employee_count': employee_count,
            'manager_name': manager_name
        })
    
    all_employees = Employee.query.all() # لجلب قائمة الموظفين للفلترة حسب المدير

    return render_template('manage_departments.html', 
                           departments=departments_data, 
                           search_query=search_query, 
                           filter_manager_id=filter_manager_id,
                           all_employees=all_employees)

@app.route('/add_department', methods=['GET', 'POST'])
def add_department():
    employees = Employee.query.all() # لجلب قائمة الموظفين لاختيار المدير
    if request.method == 'POST':
        name = request.form['name']
        description = request.form.get('description')
        manager_id = request.form.get('manager_id')
        try:
            new_department = Department(name=name, description=description)
            if manager_id and manager_id != 'None':
                new_department.manager_id = int(manager_id)
            db.session.add(new_department)
            db.session.commit()
            flash('تمت إضافة القسم بنجاح!', 'success')
            return redirect(url_for('manage_departments'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة القسم: {e}', 'danger')
    return render_template('add_department.html', employees=employees)

# تعريف نموذج الحضور
class Attendance(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(50), nullable=False) # مثلاً: حاضر, غائب, إجازة

    employee = db.relationship('Employee', backref='attendance')

    def __repr__(self):
        return f'<Attendance {self.employee_id} - {self.date} - {self.status}>'

@app.route('/manage_attendance')
def manage_attendance():
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_str = request.args.get('date', '')

    attendance_query = Attendance.query.join(Employee) # Join with Employee for searching by name

    if search_query:
        attendance_query = attendance_query.filter(Employee.full_name.ilike(f'%{search_query}%'))

    if filter_employee_id:
        attendance_query = attendance_query.filter(Attendance.employee_id == filter_employee_id)

    if filter_date_str:
        try:
            filter_date = datetime.strptime(filter_date_str, '%Y-%m-%d').date()
            attendance_query = attendance_query.filter(Attendance.date == filter_date)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح. يرجى استخدام YYYY-MM-DD.', 'danger')

    attendance_records = attendance_query.all()
    all_employees = Employee.query.all() # لجلب قائمة الموظفين للفلترة

    return render_template('manage_attendance.html',
                           attendance_records=attendance_records,
                           search_query=search_query,
                           filter_employee_id=filter_employee_id,
                           filter_date_str=filter_date_str,
                           all_employees=all_employees)

@app.route('/add_attendance', methods=['GET', 'POST'])
def add_attendance():
    employees = Employee.query.all()
    if request.method == 'POST':
        employee_id = request.form['employee_id']
        date_str = request.form['date']
        status = request.form['status']
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            new_attendance = Attendance(employee_id=employee_id, date=date_obj, status=status)
            db.session.add(new_attendance)
            db.session.commit()
            flash('تمت إضافة سجل الحضور بنجاح!', 'success')
            return redirect(url_for('manage_attendance'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة سجل الحضور: {e}', 'danger')
    return render_template('add_attendance.html', employees=employees)

@app.route('/edit_attendance/<int:attendance_id>', methods=['GET', 'POST'])
def edit_attendance(attendance_id):
    attendance = Attendance.query.get_or_404(attendance_id)
    employees = Employee.query.all()
    if request.method == 'POST':
        attendance.employee_id = request.form['employee_id']
        date_str = request.form['date']
        attendance.status = request.form['status']
        try:
            attendance.date = datetime.strptime(date_str, '%Y-%m-%d').date()
            db.session.commit()
            flash('تم تحديث سجل الحضور بنجاح!', 'success')
            return redirect(url_for('manage_attendance'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث سجل الحضور: {e}', 'danger')
    return render_template('edit_attendance.html', attendance=attendance, employees=employees)

@app.route('/delete_attendance/<int:attendance_id>', methods=['POST'])
def delete_attendance(attendance_id):
    attendance = Attendance.query.get_or_404(attendance_id)
    try:
        db.session.delete(attendance)
        db.session.commit()
        flash('تم حذف سجل الحضور بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف سجل الحضور: {e}', 'danger')
    return redirect(url_for('manage_attendance'))

# تعريف نموذج المغادرة
class Departure(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)
    departure_time = db.Column(db.Time, nullable=False) # وقت المغادرة

    employee = db.relationship('Employee', backref='departure')

    def __repr__(self):
        return f'<Departure {self.employee_id} - {self.date} - {self.departure_time}>'

# تعريف نموذج الوثائق
class Document(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    document_type = db.Column(db.String(100), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    upload_date = db.Column(db.DateTime, default=datetime.utcnow)
    employee = db.relationship('Employee', backref='documents')

    def __repr__(self):
        return f'<Document {self.document_type} for Employee {self.employee_id}>'

# تعريف نموذج الإجازات
class Leave(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    employee_id = db.Column(db.Integer, db.ForeignKey('employee.id'), nullable=False)
    leave_type = db.Column(db.String(50), nullable=False) # مثل: سنوية، مرضية، عارضة
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='Pending') # مثل: Pending, Approved, Rejected
    request_date = db.Column(db.DateTime, default=datetime.utcnow)
    employee = db.relationship('Employee', backref='leaves')

    def __repr__(self):
        return f'<Leave {self.leave_type} for Employee {self.employee_id}>'


@app.route('/manage_departure')
def manage_departure():
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_str = request.args.get('date', '')

    departure_query = Departure.query.join(Employee)

    if search_query:
        departure_query = departure_query.filter(Employee.full_name.ilike(f'%{search_query}%'))

    if filter_employee_id:
        departure_query = departure_query.filter(Departure.employee_id == filter_employee_id)

    if filter_date_str:
        try:
            filter_date = datetime.strptime(filter_date_str, '%Y-%m-%d').date()
            departure_query = departure_query.filter(Departure.date == filter_date)
        except ValueError:
            flash('تنسيق التاريخ غير صحيح. يرجى استخدام YYYY-MM-DD.', 'danger')

    departure_records = departure_query.all()
    all_employees = Employee.query.all()

    return render_template('manage_departure.html',
                           departure_records=departure_records,
                           search_query=search_query,
                           filter_employee_id=filter_employee_id,
                           filter_date_str=filter_date_str,
                           all_employees=all_employees)

@app.route('/add_departure', methods=['GET', 'POST'])
def add_departure():
    employees = Employee.query.all()
    if request.method == 'POST':
        employee_id = request.form['employee_id']
        date_str = request.form['date']
        departure_time_str = request.form['departure_time']
        try:
            date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            departure_time_obj = datetime.strptime(departure_time_str, '%H:%M').time()
            new_departure = Departure(employee_id=employee_id, date=date_obj, departure_time=departure_time_obj)
            db.session.add(new_departure)
            db.session.commit()
            flash('تمت إضافة سجل المغادرة بنجاح!', 'success')
            return redirect(url_for('manage_departure'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة سجل المغادرة: {e}', 'danger')
    return render_template('add_departure.html', employees=employees)

@app.route('/edit_departure/<int:departure_id>', methods=['GET', 'POST'])
def edit_departure(departure_id):
    departure = Departure.query.get_or_404(departure_id)
    employees = Employee.query.all()
    if request.method == 'POST':
        departure.employee_id = request.form['employee_id']
        date_str = request.form['date']
        departure_time_str = request.form['departure_time']
        try:
            departure.date = datetime.strptime(date_str, '%Y-%m-%d').date()
            departure.departure_time = datetime.strptime(departure_time_str, '%H:%M').time()
            db.session.commit()
            flash('تم تحديث سجل المغادرة بنجاح!', 'success')
            return redirect(url_for('manage_departure'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث سجل المغادرة: {e}', 'danger')
    return render_template('edit_departure.html', departure=departure, employees=employees)

@app.route('/delete_departure/<int:departure_id>', methods=['POST'])
def delete_departure(departure_id):
    departure = Departure.query.get_or_404(departure_id)
    try:
        db.session.delete(departure)
        db.session.commit()
        flash('تم حذف سجل المغادرة بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف سجل المغادرة: {e}', 'danger')
    return redirect(url_for('manage_departure'))

# مسارات إدارة الوثائق
@app.route('/manage_documents')
def manage_documents():
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_document_type = request.args.get('document_type', '')

    documents_query = Document.query.join(Employee)

    if search_query:
        documents_query = documents_query.filter(Employee.full_name.ilike(f'%{search_query}%'))

    if filter_employee_id:
        documents_query = documents_query.filter(Document.employee_id == filter_employee_id)

    if filter_document_type:
        documents_query = documents_query.filter(Document.document_type.ilike(f'%{filter_document_type}%'))

    documents = documents_query.all()
    all_employees = Employee.query.all()

    return render_template('manage_documents.html',
                           documents=documents,
                           search_query=search_query,
                           filter_employee_id=filter_employee_id,
                           filter_document_type=filter_document_type,
                           all_employees=all_employees)

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/add_document', methods=['GET', 'POST'])
@app.route('/add_document/<int:employee_id>', methods=['GET', 'POST'])
def add_document(employee_id=None):
    employees = Employee.query.all()
    selected_employee = None
    
    if employee_id:
        selected_employee = Employee.query.get_or_404(employee_id)
    
    if request.method == 'POST':
        employee_id = request.form['employee_id']
        document_type = request.form['document_type']
        
        # Get all uploaded files
        uploaded_files = request.files.getlist('document_file')

        if not uploaded_files or all(f.filename == '' for f in uploaded_files):
            flash('الرجاء اختيار ملف واحد على الأقل للتحميل.', 'danger')
            return redirect(url_for('add_document'))

        for file in uploaded_files:
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                try:
                    file.save(file_path)
                    new_document = Document(employee_id=employee_id, document_type=document_type, file_path=filename) # Save only filename
                    db.session.add(new_document)
                    db.session.commit()
                    flash(f'تمت إضافة الوثيقة {filename} بنجاح!', 'success')
                except Exception as e:
                    db.session.rollback()
                    flash(f'حدث خطأ أثناء إضافة الوثيقة {filename}: {e}', 'danger')
            else:
                flash(f'الملف {file.filename} غير مسموح به أو لا يحتوي على اسم ملف.', 'danger')
        
        return redirect(url_for('manage_documents'))
    return render_template('add_document.html', employees=employees, selected_employee=selected_employee)

@app.route('/edit_document/<int:document_id>', methods=['GET', 'POST'])
def edit_document(document_id):
    document = Document.query.get_or_404(document_id)
    employees = Employee.query.all()
    if request.method == 'POST':
        document.employee_id = request.form['employee_id']
        document.document_type = request.form['document_type']
        
        # تحقق مما إذا تم تحميل ملف جديد
        if 'document_file' in request.files and request.files['document_file'].filename != '':
            file = request.files['document_file']
            if file and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                try:
                    file.save(file_path)
                    document.file_path = filename  # حفظ اسم الملف فقط
                except Exception as e:
                    flash(f'حدث خطأ أثناء تحميل الملف: {e}', 'danger')
                    return render_template('edit_document.html', document=document, employees=employees)
            else:
                flash('نوع الملف غير مسموح به.', 'danger')
                return render_template('edit_document.html', document=document, employees=employees)
        
        try:
            db.session.commit()
            flash('تم تحديث الوثيقة بنجاح!', 'success')
            return redirect(url_for('manage_documents'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الوثيقة: {e}', 'danger')
    return render_template('edit_document.html', document=document, employees=employees)

@app.route('/delete_document/<int:document_id>', methods=['POST'])
def delete_document(document_id):
    document = Document.query.get_or_404(document_id)
    try:
        db.session.delete(document)
        db.session.commit()
        flash('تم حذف الوثيقة بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الوثيقة: {e}', 'danger')
    return redirect(url_for('manage_documents'))

@app.route('/manage_leaves')
def manage_leaves():
    # استخراج معلمات البحث والتصفية
    search_query = request.args.get('search', '')
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status', '')
    filter_date_from = request.args.get('date_from', '')
    filter_date_to = request.args.get('date_to', '')
    
    # بناء استعلام الإجازات مع الانضمام إلى جدول الموظفين
    leaves_query = Leave.query.join(Employee)
    
    # تطبيق معايير البحث والتصفية
    if search_query:
        leaves_query = leaves_query.filter(Employee.full_name.like(f'%{search_query}%'))
    
    if filter_employee_id:
        leaves_query = leaves_query.filter(Leave.employee_id == filter_employee_id)
    
    if filter_status:
        leaves_query = leaves_query.filter(Leave.status == filter_status)
    
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        leaves_query = leaves_query.filter(Leave.start_date >= date_from)
    
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        leaves_query = leaves_query.filter(Leave.end_date <= date_to)
    
    # تنفيذ الاستعلام وجلب النتائج
    leaves = leaves_query.all()
    employees = Employee.query.all()  # جلب جميع الموظفين للقائمة المنسدلة

    # حساب إحصائيات الإجازات
    total_leaves = Leave.query.count()
    pending_leaves = Leave.query.filter_by(status='Pending').count()
    approved_leaves = Leave.query.filter_by(status='Approved').count()
    rejected_leaves = Leave.query.filter_by(status='Rejected').count()
    
    return render_template('manage_leaves.html', 
                           leaves=leaves, 
                           employees=employees, 
                           search_query=search_query,
                           filter_employee_id=filter_employee_id,
                           filter_status=filter_status,
                           filter_date_from=filter_date_from,
                           filter_date_to=filter_date_to,
                           total_leaves=total_leaves,
                           pending_leaves=pending_leaves,
                           approved_leaves=approved_leaves,
                           rejected_leaves=rejected_leaves)

@app.route('/add_leave', methods=['GET', 'POST'])
def add_leave():
    employees = Employee.query.all()
    if request.method == 'POST':
        try:
            employee_id = request.form['employee_id']
            leave_type = request.form['leave_type']
            start_date_str = request.form['start_date']
            end_date_str = request.form['end_date']
            status = request.form['status']

            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()

            # التحقق من صحة التواريخ
            if start_date > end_date:
                flash('تاريخ البدء يجب أن يكون قبل تاريخ الانتهاء!', 'danger')
                return redirect(url_for('manage_leaves'))

            new_leave = Leave(employee_id=employee_id, leave_type=leave_type, 
                             start_date=start_date, end_date=end_date, status=status)
            db.session.add(new_leave)
            db.session.commit()
            flash('تمت إضافة طلب الإجازة بنجاح!', 'success')
            return redirect(url_for('manage_leaves'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إضافة طلب الإجازة: {e}', 'danger')
            return redirect(url_for('manage_leaves'))
    return render_template('add_leave.html', employees=employees)

@app.route('/delete_leave/<int:leave_id>', methods=['POST'])
def delete_leave(leave_id):
    try:
        leave = Leave.query.get_or_404(leave_id)
        employee_name = leave.employee.full_name  # حفظ اسم الموظف قبل الحذف
        leave_type = leave.leave_type  # حفظ نوع الإجازة قبل الحذف
        db.session.delete(leave)
        db.session.commit()
        flash(f'تم حذف طلب الإجازة ({leave_type}) للموظف {employee_name} بنجاح!', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف طلب الإجازة: {e}', 'danger')
    return redirect(url_for('manage_leaves'))

@app.route('/edit_leave/<int:leave_id>', methods=['GET', 'POST'])
def edit_leave(leave_id):
    leave = Leave.query.get_or_404(leave_id)
    employees = Employee.query.all()
    
    if request.method == 'POST':
        leave.employee_id = request.form['employee_id']
        leave.leave_type = request.form['leave_type']
        start_date_str = request.form['start_date']
        end_date_str = request.form['end_date']
        leave.status = request.form['status']
        
        try:
            leave.start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            leave.end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            db.session.commit()
            flash('تم تحديث طلب الإجازة بنجاح!', 'success')
            return redirect(url_for('manage_leaves'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث طلب الإجازة: {e}', 'danger')
    
    return render_template('edit_leave.html', 
                         leave=leave, 
                         employees=employees,
                         leave_types=['سنوية', 'مرضية', 'عارضة'],
                         statuses=['Pending', 'Approved', 'Rejected'])

# مسار تقرير الحضور
@app.route('/attendance_report')
def attendance_report_route():
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status')
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    
    # استعلام قاعدة البيانات للحصول على سجلات الحضور
    query = Attendance.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Attendance.employee_id == filter_employee_id)
    if filter_status:
        query = query.filter(Attendance.status == filter_status)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Attendance.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Attendance.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # ترتيب النتائج حسب التاريخ تنازلياً
    attendance_records = query.order_by(Attendance.date.desc()).all()
    
    # حساب الإحصائيات
    stats = {
        'present': query.filter(Attendance.status == 'حاضر').count(),
        'absent': query.filter(Attendance.status == 'غائب').count(),
        'leave': query.filter(Attendance.status == 'إجازة').count(),
        'mission': query.filter(Attendance.status == 'مهمة رسمية').count()
    }
    
    # حساب إحصائيات الحضور الشهرية
    monthly_stats = calculate_monthly_attendance_stats(query)
    
    # إعداد ملخص الحضور حسب الموظف
    employee_summary = calculate_employee_attendance_summary(query)
    
    # الحصول على قوائم الموظفين والأقسام للتصفية
    all_employees = Employee.query.order_by(Employee.full_name).all()
    all_departments = Department.query.order_by(Department.name).all()
    
    return render_template('attendance_report.html',
                          attendance_records=attendance_records,
                          stats=stats,
                          monthly_stats=monthly_stats,
                          employee_summary=employee_summary,
                          all_employees=all_employees,
                          all_departments=all_departments,
                          filter_employee_id=filter_employee_id,
                          filter_status=filter_status,
                          filter_date_from=filter_date_from,
                          filter_date_to=filter_date_to,
                          filter_department_id=filter_department_id)

def calculate_monthly_attendance_stats(query):
    """حساب إحصائيات الحضور الشهرية"""
    # إنشاء قاموس لتخزين عدد أيام الحضور والغياب لكل شهر
    monthly_present = defaultdict(int)
    monthly_total = defaultdict(int)
    
    # الحصول على جميع سجلات الحضور
    all_records = query.all()
    
    # حساب عدد أيام الحضور والإجمالي لكل شهر
    for record in all_records:
        month = record.date.month - 1  # الفهرس يبدأ من 0
        monthly_total[month] += 1
        if record.status == 'حاضر':
            monthly_present[month] += 1
    
    # حساب نسبة الحضور لكل شهر
    monthly_rates = [0] * 12
    for month in range(12):
        if monthly_total[month] > 0:
            monthly_rates[month] = round((monthly_present[month] / monthly_total[month]) * 100)
    
    return monthly_rates

def calculate_employee_attendance_summary(query):
    """حساب ملخص الحضور لكل موظف"""
    # إنشاء قاموس لتخزين بيانات الحضور لكل موظف
    employee_data = defaultdict(lambda: {
        'present_days': 0,
        'absent_days': 0,
        'leave_days': 0,
        'mission_days': 0,
        'total_days': 0
    })
    
    # الحصول على جميع سجلات الحضور
    all_records = query.all()
    
    # حساب عدد أيام كل حالة لكل موظف
    for record in all_records:
        employee_id = record.employee_id
        employee_data[employee_id]['total_days'] += 1
        
        if record.status == 'حاضر':
            employee_data[employee_id]['present_days'] += 1
        elif record.status == 'غائب':
            employee_data[employee_id]['absent_days'] += 1
        elif record.status == 'إجازة':
            employee_data[employee_id]['leave_days'] += 1
        elif record.status == 'مهمة رسمية':
            employee_data[employee_id]['mission_days'] += 1
    
    # إعداد قائمة ملخص الحضور لكل موظف
    employee_summary = []
    for employee_id, data in employee_data.items():
        employee = Employee.query.get(employee_id)
        if employee:
            # حساب نسبة الحضور
            attendance_percentage = 0
            if data['total_days'] > 0:
                attendance_percentage = round((data['present_days'] / data['total_days']) * 100)
            
            employee_summary.append({
                'employee_name': employee.full_name,
                'employee_id': employee.employee_id,
                'department': employee.department.name if employee.department else 'غير محدد',
                'present_days': data['present_days'],
                'absent_days': data['absent_days'],
                'leave_days': data['leave_days'],
                'mission_days': data['mission_days'],
                'attendance_percentage': attendance_percentage
            })
    
    # ترتيب الملخص حسب نسبة الحضور تنازلياً
    employee_summary.sort(key=lambda x: x['attendance_percentage'], reverse=True)
    
    return employee_summary

# مسار تصدير تقرير الحضور
@app.route('/export_attendance_report')
def export_attendance_report_route():
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_status = request.args.get('status')
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    
    # استعلام قاعدة البيانات للحصول على سجلات الحضور
    query = Attendance.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Attendance.employee_id == filter_employee_id)
    if filter_status:
        query = query.filter(Attendance.status == filter_status)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Attendance.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Attendance.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # ترتيب النتائج حسب التاريخ تنازلياً
    attendance_records = query.order_by(Attendance.date.desc()).all()
    
    # إنشاء DataFrame لتقرير الحضور
    data = []
    for record in attendance_records:
        data.append({
            'اسم الموظف': record.employee.full_name,
            'الرقم الوظيفي': record.employee.employee_id,
            'القسم': record.employee.department.name if record.employee.department else 'غير محدد',
            'التاريخ': record.date.strftime('%Y-%m-%d'),
            'الحالة': record.status,
            'ملاحظات': record.notes or ''
        })
    
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel مؤقت
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    # حفظ البيانات في ملف Excel
    df.to_excel(temp_filename, index=False, engine='openpyxl')
    
    # إرسال الملف للتنزيل
    return send_file(temp_filename, 
                     as_attachment=True,
                     download_name='تقرير_الحضور.xlsx',
                     mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

# مسار تقرير المغادرة
@app.route('/departure_report')
def departure_report_route():
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    filter_time_from = request.args.get('time_from')
    filter_time_to = request.args.get('time_to')
    
    # استعلام قاعدة البيانات للحصول على سجلات المغادرة
    query = Departure.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Departure.employee_id == filter_employee_id)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Departure.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Departure.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # تطبيق تصفية الوقت إذا تم تحديدها
    departure_records = query.order_by(Departure.date.desc()).all()
    if filter_time_from or filter_time_to:
        filtered_records = []
        for record in departure_records:
            time_str = record.departure_time.strftime('%H:%M')
            if filter_time_from and filter_time_to:
                if filter_time_from <= time_str <= filter_time_to:
                    filtered_records.append(record)
            elif filter_time_from:
                if filter_time_from <= time_str:
                    filtered_records.append(record)
            elif filter_time_to:
                if time_str <= filter_time_to:
                    filtered_records.append(record)
        departure_records = filtered_records
    
    # حساب الإحصائيات
    today = datetime.now().date()
    week_start = today - timedelta(days=today.weekday())
    month_start = today.replace(day=1)
    
    stats = {
        'today': sum(1 for r in departure_records if r.date == today),
        'week': sum(1 for r in departure_records if r.date >= week_start),
        'month': sum(1 for r in departure_records if r.date >= month_start),
        'total': len(departure_records)
    }
    
    # حساب إحصائيات المغادرة الشهرية
    monthly_stats = calculate_monthly_departure_stats(query)
    
    # إعداد بيانات الخريطة الحرارية
    heatmap_data = calculate_departure_heatmap_data(query)
    
    # إعداد ملخص المغادرة حسب الموظف
    employee_summary = calculate_employee_departure_summary(query)
    
    # الحصول على قوائم الموظفين والأقسام للتصفية
    all_employees = Employee.query.order_by(Employee.full_name).all()
    all_departments = Department.query.order_by(Department.name).all()
    
    return render_template('departure_report.html',
                          departure_records=departure_records,
                          stats=stats,
                          monthly_stats=monthly_stats,
                          heatmap_data=heatmap_data,
                          employee_summary=employee_summary,
                          all_employees=all_employees,
                          all_departments=all_departments,
                          filter_employee_id=filter_employee_id,
                          filter_date_from=filter_date_from,
                          filter_date_to=filter_date_to,
                          filter_department_id=filter_department_id,
                          filter_time_from=filter_time_from,
                          filter_time_to=filter_time_to)

def calculate_monthly_departure_stats(query):
    """حساب إحصائيات المغادرة الشهرية"""
    # إنشاء قاموس لتخزين عدد المغادرات لكل شهر
    monthly_departures = [0] * 12
    
    # الحصول على جميع سجلات المغادرة
    all_records = query.all()
    
    # حساب عدد المغادرات لكل شهر
    for record in all_records:
        month = record.date.month - 1  # الفهرس يبدأ من 0
        monthly_departures[month] += 1
    
    return monthly_departures

def calculate_departure_heatmap_data(query):
    """إعداد بيانات الخريطة الحرارية لأوقات المغادرة"""
    # إنشاء مصفوفة لتخزين عدد المغادرات لكل يوم وساعة
    heatmap_data = [[0 for _ in range(8)] for _ in range(5)]  # 5 أيام × 8 ساعات
    
    # الحصول على جميع سجلات المغادرة
    all_records = query.all()
    
    # حساب عدد المغادرات لكل يوم وساعة
    for record in all_records:
        weekday = record.date.weekday()
        if weekday < 5:  # فقط أيام الأسبوع (الأحد إلى الخميس)
            hour = record.departure_time.hour
            if 8 <= hour < 16:  # ساعات العمل (8 صباحاً إلى 4 مساءً)
                hour_index = hour - 8
                heatmap_data[weekday][hour_index] += 1
    
    return heatmap_data

def calculate_employee_departure_summary(query):
    """حساب ملخص المغادرة لكل موظف"""
    # إنشاء قاموس لتخزين بيانات المغادرة لكل موظف
    employee_data = defaultdict(lambda: {
        'departure_count': 0,
        'departure_times': [],
        'departure_days': defaultdict(int)
    })
    
    # الحصول على جميع سجلات المغادرة
    all_records = query.all()
    
    # حساب بيانات المغادرة لكل موظف
    for record in all_records:
        employee_id = record.employee_id
        employee_data[employee_id]['departure_count'] += 1
        employee_data[employee_id]['departure_times'].append(record.departure_time)
        weekday = record.date.weekday()
        employee_data[employee_id]['departure_days'][weekday] += 1
    
    # إعداد قائمة ملخص المغادرة لكل موظف
    employee_summary = []
    weekday_names = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت']
    
    for employee_id, data in employee_data.items():
        employee = Employee.query.get(employee_id)
        if employee:
            # حساب متوسط وقت المغادرة
            average_time = '00:00'
            earliest_time = '00:00'
            most_frequent_day = 'غير محدد'
            
            if data['departure_times']:
                # حساب متوسط الوقت
                total_seconds = sum(dt.hour * 3600 + dt.minute * 60 + dt.second for dt in data['departure_times'])
                avg_seconds = total_seconds // len(data['departure_times'])
                avg_hours, remainder = divmod(avg_seconds, 3600)
                avg_minutes, avg_seconds = divmod(remainder, 60)
                average_time = f'{avg_hours:02d}:{avg_minutes:02d}'
                
                # تحديد أبكر وقت مغادرة
                earliest = min(data['departure_times'], key=lambda x: x.hour * 60 + x.minute)
                earliest_time = earliest.strftime('%H:%M')
            
            # تحديد أكثر يوم للمغادرة
            if data['departure_days']:
                most_frequent_day_index = max(data['departure_days'], key=data['departure_days'].get)
                most_frequent_day = weekday_names[most_frequent_day_index]
            
            employee_summary.append({
                'employee_name': employee.full_name,
                'employee_id': employee.employee_id,
                'department': employee.department.name if employee.department else 'غير محدد',
                'departure_count': data['departure_count'],
                'average_time': average_time,
                'earliest_time': earliest_time,
                'most_frequent_day': most_frequent_day
            })
    
    # ترتيب الملخص حسب عدد المغادرات تنازلياً
    employee_summary.sort(key=lambda x: x['departure_count'], reverse=True)
    
    return employee_summary

# مسار تصدير تقرير المغادرة
@app.route('/export_departure_report')
def export_departure_report_route():
    # الحصول على معايير التصفية من الطلب
    filter_employee_id = request.args.get('employee_id', type=int)
    filter_date_from = request.args.get('date_from')
    filter_date_to = request.args.get('date_to')
    filter_department_id = request.args.get('department_id', type=int)
    filter_time_from = request.args.get('time_from')
    filter_time_to = request.args.get('time_to')
    
    # استعلام قاعدة البيانات للحصول على سجلات المغادرة
    query = Departure.query.join(Employee).outerjoin(Department, Employee.department_id == Department.id)
    
    # تطبيق التصفية
    if filter_employee_id:
        query = query.filter(Departure.employee_id == filter_employee_id)
    if filter_date_from:
        date_from = datetime.strptime(filter_date_from, '%Y-%m-%d').date()
        query = query.filter(Departure.date >= date_from)
    if filter_date_to:
        date_to = datetime.strptime(filter_date_to, '%Y-%m-%d').date()
        query = query.filter(Departure.date <= date_to)
    if filter_department_id:
        query = query.filter(Employee.department_id == filter_department_id)
    
    # الحصول على سجلات المغادرة
    departure_records = query.order_by(Departure.date.desc()).all()
    
    # تطبيق تصفية الوقت إذا تم تحديدها
    if filter_time_from or filter_time_to:
        filtered_records = []
        for record in departure_records:
            time_str = record.departure_time.strftime('%H:%M')
            if filter_time_from and filter_time_to:
                if filter_time_from <= time_str <= filter_time_to:
                    filtered_records.append(record)
            elif filter_time_from:
                if filter_time_from <= time_str:
                    filtered_records.append(record)
            elif filter_time_to:
                if time_str <= filter_time_to:
                    filtered_records.append(record)
        departure_records = filtered_records
    
    # إنشاء DataFrame لتقرير المغادرة
    data = []
    for record in departure_records:
        data.append({
            'اسم الموظف': record.employee.full_name,
            'الرقم الوظيفي': record.employee.employee_id,
            'القسم': record.employee.department.name if record.employee.department else 'غير محدد',
            'التاريخ': record.date.strftime('%Y-%m-%d'),
            'وقت المغادرة': record.departure_time.strftime('%H:%M'),
            'سبب المغادرة': record.reason or ''
        })
    
    df = pd.DataFrame(data)
    
    # إنشاء ملف Excel مؤقت
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    # حفظ البيانات في ملف Excel
    df.to_excel(temp_filename, index=False, engine='openpyxl')
    
    # إرسال الملف للتنزيل
    return send_file(temp_filename, 
                     as_attachment=True,
                     download_name='تقرير_المغادرة.xlsx',
                     mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

if __name__ == '__main__':
    with app.app_context():
        db.create_all() # إنشاء الجداول في قاعدة البيانات
    app.run(debug=True)