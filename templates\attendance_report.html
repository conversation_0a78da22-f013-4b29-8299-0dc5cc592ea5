<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الحضور</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            font-family: 'Ta<PERSON>wal', 'Arial', sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            transition: all 0.3s ease;
        }
        .wrapper {
            display: flex;
            width: 100%;
            align-items: stretch;
        }
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            background: #343a40;
            color: #fff;
            transition: all 0.3s;
            height: 100vh;
            position: fixed;
            z-index: 999;
        }
        #sidebar.active {
            margin-right: -250px;
        }
        #sidebar .sidebar-header {
            padding: 20px;
            background: #212529;
        }
        #sidebar ul.components {
            padding: 20px 0;
            border-bottom: 1px solid #4b545c;
        }
        #sidebar ul p {
            color: #fff;
            padding: 10px;
        }
        #sidebar ul li a {
            padding: 10px 15px;
            font-size: 1.1em;
            display: block;
            color: #fff;
            text-decoration: none;
            transition: all 0.3s;
        }
        #sidebar ul li a:hover {
            color: #343a40;
            background: #fff;
        }
        #sidebar ul li.active > a {
            color: #fff;
            background: #007bff;
        }
        #content {
            width: 100%;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s;
            margin-right: 250px;
        }
        #content.active {
            margin-right: 0;
        }
        .card {
            border-radius: 0.75rem;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            border: none;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1.25rem 1.5rem;
            border-radius: 0.75rem 0.75rem 0 0 !important;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .card-body {
            padding: 1.5rem;
        }
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }
        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }
        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        .form-control {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            border: 1px solid #ced4da;
        }
        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .table {
            background-color: #fff;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.05rem;
        }
        .table td, .table th {
            padding: 1rem;
            vertical-align: middle;
        }
        .badge {
            padding: 0.5rem 0.75rem;
            font-weight: 500;
            border-radius: 0.5rem;
        }
        .search-form {
            background-color: #fff;
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }
        .report-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        .stat-card {
            flex: 1;
            min-width: 200px;
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }
        .stat-card .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .stat-card .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stat-card .stat-label {
            font-size: 1rem;
            color: #6c757d;
        }
        .stat-card.present {
            border-top: 4px solid #28a745;
        }
        .stat-card.present .stat-icon {
            color: #28a745;
        }
        .stat-card.absent {
            border-top: 4px solid #dc3545;
        }
        .stat-card.absent .stat-icon {
            color: #dc3545;
        }
        .stat-card.leave {
            border-top: 4px solid #ffc107;
        }
        .stat-card.leave .stat-icon {
            color: #ffc107;
        }
        .stat-card.mission {
            border-top: 4px solid #17a2b8;
        }
        .stat-card.mission .stat-icon {
            color: #17a2b8;
        }
        .chart-container {
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }
        .chart-container h5 {
            margin-bottom: 1.5rem;
            text-align: center;
        }
        .action-buttons {
            white-space: nowrap;
        }
        .action-buttons .btn {
            padding: 0.375rem 0.75rem;
            margin: 0 0.25rem;
        }
        .print-btn {
            margin-bottom: 1rem;
        }
        @media print {
            #sidebar, .navbar, .search-form, .no-print {
                display: none !important;
            }
            #content {
                margin-right: 0 !important;
                padding: 0 !important;
            }
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }
            body {
                background-color: white !important;
            }
        }
        @media (max-width: 768px) {
            #sidebar {
                margin-right: -250px;
            }
            #sidebar.active {
                margin-right: 0;
            }
            #content {
                margin-right: 0;
            }
            #content.active {
                margin-right: 250px;
            }
            #sidebarCollapse span {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar  -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <h3>نظام إدارة الموظفين</h3>
            </div>

            <ul class="list-unstyled components">
                <li>
                    <a href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> الرئيسية</a>
                </li>
                <li>
                    <a href="{{ url_for('view_employees') }}"><i class="fas fa-users"></i> إدارة الموظفين</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departments') }}"><i class="fas fa-building"></i> إدارة الأقسام</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_attendance') }}"><i class="fas fa-check-circle"></i> إدارة الحضور</a>
                </li>
                <li class="active">
                    <a href="{{ url_for('attendance_report_route') }}"><i class="fas fa-file-alt"></i> تقارير الحضور</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departure') }}"><i class="fas fa-sign-out-alt"></i> إدارة المغادرة</a>
                </li>
                <li>
                    <a href="{{ url_for('departure_report_route') }}"><i class="fas fa-file-export"></i> تقارير المغادرة</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_documents') }}"><i class="fas fa-file-alt"></i> الوثائق</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_leaves') }}"><i class="fas fa-calendar-alt"></i> الإجازات</a>
                </li>
                <li>
                    <a href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                </li>
            </ul>
        </nav>

        <!-- Page Content  -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-align-right"></i>
                        <span>تبديل الشريط الجانبي</span>
                    </button>
                    <div class="ml-auto">
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </nav>

            <h2 class="mb-4 text-center">تقارير الحضور</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- إحصائيات الحضور -->
            <div class="report-stats">
                <div class="stat-card present">
                    <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="stat-value">{{ stats.present if stats else 0 }}</div>
                    <div class="stat-label">حاضر</div>
                </div>
                <div class="stat-card absent">
                    <div class="stat-icon"><i class="fas fa-times-circle"></i></div>
                    <div class="stat-value">{{ stats.absent if stats else 0 }}</div>
                    <div class="stat-label">غائب</div>
                </div>
                <div class="stat-card leave">
                    <div class="stat-icon"><i class="fas fa-calendar-minus"></i></div>
                    <div class="stat-value">{{ stats.leave if stats else 0 }}</div>
                    <div class="stat-label">إجازة</div>
                </div>
                <div class="stat-card mission">
                    <div class="stat-icon"><i class="fas fa-briefcase"></i></div>
                    <div class="stat-value">{{ stats.mission if stats else 0 }}</div>
                    <div class="stat-label">مهمة رسمية</div>
                </div>
            </div>

            <!-- زر الطباعة -->
            <div class="print-btn no-print">
                <button onclick="window.print()" class="btn btn-primary"><i class="fas fa-print"></i> طباعة التقرير</button>
                <a href="{{ url_for('export_attendance_report_route') }}" class="btn btn-success"><i class="fas fa-file-excel"></i> تصدير إلى Excel</a>
            </div>

            <!-- نموذج البحث والتصفية -->
            <div class="search-form no-print">
                <form class="form-row" method="GET" action="{{ url_for('attendance_report_route') }}">
                    <div class="form-group col-md-3">
                        <label for="employee_id">الموظف</label>
                        <select class="form-control" id="employee_id" name="employee_id">
                            <option value="">جميع الموظفين</option>
                            {% for employee in all_employees %}
                                <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>{{ employee.full_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <label for="status">الحالة</label>
                        <select class="form-control" id="status" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="حاضر" {% if filter_status == 'حاضر' %}selected{% endif %}>حاضر</option>
                            <option value="غائب" {% if filter_status == 'غائب' %}selected{% endif %}>غائب</option>
                            <option value="إجازة" {% if filter_status == 'إجازة' %}selected{% endif %}>إجازة</option>
                            <option value="مهمة رسمية" {% if filter_status == 'مهمة رسمية' %}selected{% endif %}>مهمة رسمية</option>
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <label for="date_from">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ filter_date_from }}">
                    </div>
                    <div class="form-group col-md-2">
                        <label for="date_to">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ filter_date_to }}">
                    </div>
                    <div class="form-group col-md-3">
                        <label for="department_id">القسم</label>
                        <select class="form-control" id="department_id" name="department_id">
                            <option value="">جميع الأقسام</option>
                            {% for department in all_departments %}
                                <option value="{{ department.id }}" {% if filter_department_id == department.id %}selected{% endif %}>{{ department.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group col-md-12 text-center">
                        <button type="submit" class="btn btn-primary mx-2"><i class="fas fa-search"></i> بحث وتصفية</button>
                        <a href="{{ url_for('attendance_report_route') }}" class="btn btn-secondary mx-2"><i class="fas fa-sync-alt"></i> مسح التصفية</a>
                    </div>
                </form>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>توزيع حالات الحضور</h5>
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>معدل الحضور الشهري</h5>
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- جدول تقرير الحضور -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list mr-2"></i> تقرير الحضور</h5>
                </div>
                <div class="card-body">
                    {% if attendance_records %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الموظف</th>
                                        <th>الرقم الوظيفي</th>
                                        <th>القسم</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in attendance_records %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ record.employee.full_name }}</td>
                                            <td>{{ record.employee.employee_id }}</td>
                                            <td>{{ record.employee.department.name if record.employee.department else 'غير محدد' }}</td>
                                            <td>{{ record.date.strftime('%Y-%m-%d') }}</td>
                                            <td>
                                                {% if record.status == 'حاضر' %}
                                                    <span class="badge badge-success">{{ record.status }}</span>
                                                {% elif record.status == 'غائب' %}
                                                    <span class="badge badge-danger">{{ record.status }}</span>
                                                {% elif record.status == 'إجازة' %}
                                                    <span class="badge badge-warning">{{ record.status }}</span>
                                                {% elif record.status == 'مهمة رسمية' %}
                                                    <span class="badge badge-info">{{ record.status }}</span>
                                                {% else %}
                                                    {{ record.status }}
                                                {% endif %}
                                            </td>
                                            <td>{{ record.notes if record.notes else '-' }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد سجلات حضور تطابق معايير البحث.
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- ملخص الحضور حسب الموظف -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-check mr-2"></i> ملخص الحضور حسب الموظف</h5>
                </div>
                <div class="card-body">
                    {% if employee_summary %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الموظف</th>
                                        <th>الرقم الوظيفي</th>
                                        <th>القسم</th>
                                        <th>أيام الحضور</th>
                                        <th>أيام الغياب</th>
                                        <th>أيام الإجازة</th>
                                        <th>أيام المهمات</th>
                                        <th>نسبة الحضور</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for summary in employee_summary %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ summary.employee_name }}</td>
                                            <td>{{ summary.employee_id }}</td>
                                            <td>{{ summary.department }}</td>
                                            <td>{{ summary.present_days }}</td>
                                            <td>{{ summary.absent_days }}</td>
                                            <td>{{ summary.leave_days }}</td>
                                            <td>{{ summary.mission_days }}</td>
                                            <td>
                                                <div class="progress">
                                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ summary.attendance_percentage }}%" aria-valuenow="{{ summary.attendance_percentage }}" aria-valuemin="0" aria-valuemax="100">{{ summary.attendance_percentage }}%</div>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد بيانات ملخصة متاحة.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        $(document).ready(function () {
            $('#sidebarCollapse').on('click', function () {
                $('#sidebar, #content').toggleClass('active');
            });
        });

        // رسم بياني لتوزيع حالات الحضور
        var statusCtx = document.getElementById('statusChart').getContext('2d');
        var statusChart = new Chart(statusCtx, {
            type: 'pie',
            data: {
                labels: ['حاضر', 'غائب', 'إجازة', 'مهمة رسمية'],
                datasets: [{
                    data: [
                        {{ stats.present if stats else 0 }},
                        {{ stats.absent if stats else 0 }},
                        {{ stats.leave if stats else 0 }},
                        {{ stats.mission if stats else 0 }}
                    ],
                    backgroundColor: [
                        '#28a745',
                        '#dc3545',
                        '#ffc107',
                        '#17a2b8'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom',
                    rtl: true,
                    labels: {
                        fontFamily: 'Tajawal'
                    }
                }
            }
        });

        // رسم بياني للحضور الشهري
        var monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        var monthlyChart = new Chart(monthlyCtx, {
            type: 'bar',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'نسبة الحضور',
                    data: {{ monthly_stats|safe if monthly_stats else '[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]' }},
                    backgroundColor: '#007bff',
                    borderColor: '#007bff',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            max: 100,
                            callback: function(value) {return value + '%'}
                        },
                        scaleLabel: {
                            display: true,
                            labelString: 'نسبة الحضور'
                        }
                    }]
                },
                legend: {
                    display: false
                }
            }
        });
    </script>
</body>
</html>