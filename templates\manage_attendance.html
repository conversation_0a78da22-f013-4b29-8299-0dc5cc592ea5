<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحضور</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            font-family: '<PERSON><PERSON>wal', 'Arial', sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            transition: all 0.3s ease;
        }
        .wrapper {
            display: flex;
            width: 100%;
            align-items: stretch;
        }
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            background: #2c3e50;
            color: #fff;
            transition: all 0.3s;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        #sidebar.active {
            margin-right: -250px;
        }
        #sidebar .sidebar-header {
            padding: 20px;
            background: #34495e;
            text-align: center;
        }
        #sidebar .sidebar-header h3 {
            color: #fff;
            margin: 0;
            font-size: 1.5em;
        }
        #sidebar ul.components {
            padding: 20px 0;
            border-bottom: 1px solid #3a536e;
        }
        #sidebar ul li a {
            padding: 15px 20px;
            font-size: 1.1em;
            display: block;
            color: #e0e0e0;
            border-bottom: 1px solid rgba(255,255,255,0.05);
            transition: all 0.3s ease;
        }
        #sidebar ul li a:hover {
            color: #fff;
            background: #1abc9c;
            text-decoration: none;
            transform: translateX(-5px);
        }
        #sidebar ul li.active > a, a[aria-expanded="true"] {
            color: #fff;
            background: #16a085;
        }
        #content {
            width: 100%;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s;
        }
        .navbar {
            background-color: #fff;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .card {
            margin-bottom: 20px;
            border: none;
            border-radius: 0.75rem;
            box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.08);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.8rem 2rem rgba(0, 0, 0, 0.12);
        }
        .card-header {
            background-color: #3498db;
            color: #fff;
            border-bottom: none;
            border-top-left-radius: 0.75rem;
            border-top-right-radius: 0.75rem;
            font-size: 1.25rem;
            padding: 1rem 1.25rem;
        }
        .table thead th {
            background-color: #3498db;
            color: #fff;
            border-color: #3498db;
            vertical-align: middle;
            padding: 0.75rem;
        }
        .table tbody tr {
            transition: background-color 0.2s ease;
        }
        .table tbody tr:hover {
            background-color: #f5f5f5;
        }
        .table-bordered th, .table-bordered td {
            border: 1px solid #dee2e6;
            vertical-align: middle;
        }
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .btn-primary {
            background-color: #28a745;
            border-color: #28a745;
        }
        .btn-primary:hover {
            background-color: #218838;
            border-color: #1e7e34;
        }
        .btn-success {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }
        .btn-success:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        .btn-info {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #343a40;
        }
        .btn-info:hover {
            background-color: #e0a800;
            border-color: #d39e00;
        }
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        .form-control {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        .alert {
            border-radius: 0.5rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
        }
        .badge {
            padding: 0.5em 0.75em;
            border-radius: 0.5rem;
            font-weight: 500;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #343a40;
        }
        .badge-info {
            background-color: #17a2b8;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 5px;
        }
        .action-buttons .btn {
            width: 36px;
            height: 36px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .search-form {
            background-color: #fff;
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }
        .attendance-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        .stat-card {
            flex: 1;
            min-width: 200px;
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.25rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }
        .stat-card .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 0.75rem;
        }
        .stat-card .stat-value {
            font-size: 1.75rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        .stat-card .stat-label {
            color: #6c757d;
            font-size: 1rem;
        }
        .stat-card.present {
            border-top: 4px solid #28a745;
        }
        .stat-card.present .stat-icon {
            color: #28a745;
        }
        .stat-card.absent {
            border-top: 4px solid #dc3545;
        }
        .stat-card.absent .stat-icon {
            color: #dc3545;
        }
        .stat-card.leave {
            border-top: 4px solid #ffc107;
        }
        .stat-card.leave .stat-icon {
            color: #ffc107;
        }
        .stat-card.mission {
            border-top: 4px solid #17a2b8;
        }
        .stat-card.mission .stat-icon {
            color: #17a2b8;
        }
        @media (max-width: 768px) {
            #sidebar {
                margin-right: -250px;
            }
            #sidebar.active {
                margin-right: 0;
            }
            #sidebarCollapse span {
                display: none;
            }
            .attendance-stats {
                flex-direction: column;
            }
            .stat-card {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar">
            <div class="sidebar-header text-center">
                <h3>نظام إدارة الموارد البشرية</h3>
            </div>
            <ul class="list-unstyled components">
                <li>
                    <a href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> لوحة التحكم</a>
                </li>
                <li>
                    <a href="{{ url_for('view_employees') }}"><i class="fas fa-users"></i> إدارة الموظفين</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departments') }}"><i class="fas fa-building"></i> إدارة الأقسام</a>
                </li>
                <li class="active">
                    <a href="{{ url_for('manage_attendance') }}"><i class="fas fa-check-circle"></i> إدارة الحضور</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_documents') }}"><i class="fas fa-file-alt"></i> الوثائق</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departure') }}"><i class="fas fa-plane-departure"></i> المغادرات</a>
                </li>
                <li>
                    <a href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                </li>
            </ul>
        </nav>

        <!-- Page Content -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-align-right"></i>
                        <span>تبديل الشريط الجانبي</span>
                    </button>
                    <div class="ml-auto">
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </nav>

            <h2 class="mb-4 text-center">إدارة سجلات الحضور</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Attendance Statistics -->
            <div class="attendance-stats">
                <div class="stat-card present">
                    <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="stat-value">{{ stats.present if stats else 0 }}</div>
                    <div class="stat-label">حاضر</div>
                </div>
                <div class="stat-card absent">
                    <div class="stat-icon"><i class="fas fa-times-circle"></i></div>
                    <div class="stat-value">{{ stats.absent if stats else 0 }}</div>
                    <div class="stat-label">غائب</div>
                </div>
                <div class="stat-card leave">
                    <div class="stat-icon"><i class="fas fa-calendar-minus"></i></div>
                    <div class="stat-value">{{ stats.leave if stats else 0 }}</div>
                    <div class="stat-label">إجازة</div>
                </div>
                <div class="stat-card mission">
                    <div class="stat-icon"><i class="fas fa-briefcase"></i></div>
                    <div class="stat-value">{{ stats.mission if stats else 0 }}</div>
                    <div class="stat-label">مهمة رسمية</div>
                </div>
            </div>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list mr-2"></i> سجلات الحضور</h5>
                    <div>
                        <a href="{{ url_for('add_attendance') }}" class="btn btn-success"><i class="fas fa-plus"></i> إضافة سجل حضور جديد</a>
                        <a href="{{ url_for('attendance_report_route') }}" class="btn btn-primary"><i class="fas fa-file-alt"></i> تقرير الحضور</a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Form -->
                    <div class="search-form">
                        <form class="form-row" method="GET" action="{{ url_for('manage_attendance') }}">
                            <div class="form-group col-md-3">
                                <label for="search"><i class="fas fa-search"></i> بحث باسم الموظف</label>
                                <input type="text" class="form-control" id="search" name="search" placeholder="اسم الموظف" value="{{ search_query if search_query else '' }}">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="employee_id"><i class="fas fa-user"></i> تصفية حسب الموظف</label>
                                <select class="form-control" id="employee_id" name="employee_id">
                                    <option value="">اختر موظف...</option>
                                    {% for employee in all_employees %}
                                        <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>{{ employee.full_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="form-group col-md-3">
                                <label for="date"><i class="fas fa-calendar-alt"></i> تصفية حسب التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" value="{{ filter_date_str if filter_date_str else '' }}">
                            </div>
                            <div class="form-group col-md-3">
                                <label for="status"><i class="fas fa-filter"></i> تصفية حسب الحالة</label>
                                <select class="form-control" id="status" name="status">
                                    <option value="">جميع الحالات</option>
                                    <option value="حاضر" {% if filter_status == 'حاضر' %}selected{% endif %}>حاضر</option>
                                    <option value="غائب" {% if filter_status == 'غائب' %}selected{% endif %}>غائب</option>
                                    <option value="إجازة" {% if filter_status == 'إجازة' %}selected{% endif %}>إجازة</option>
                                    <option value="مهمة رسمية" {% if filter_status == 'مهمة رسمية' %}selected{% endif %}>مهمة رسمية</option>
                                </select>
                            </div>
                            <div class="form-group col-12 text-center mt-3">
                                <button type="submit" class="btn btn-primary mx-2"><i class="fas fa-search"></i> بحث وتصفية</button>
                                <a href="{{ url_for('manage_attendance') }}" class="btn btn-secondary mx-2"><i class="fas fa-sync-alt"></i> مسح التصفية</a>
                            </div>
                        </form>
                    </div>

                    {% if attendance_records %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover text-center">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in attendance_records %}
                                        <tr>
                                            <td>{{ record.employee.full_name }}</td>
                                            <td>{{ record.date.strftime('%Y-%m-%d') }}</td>
                                            <td>
                                                {% if record.status == 'حاضر' %}
                                                    <span class="badge badge-success">{{ record.status }}</span>
                                                {% elif record.status == 'غائب' %}
                                                    <span class="badge badge-danger">{{ record.status }}</span>
                                                {% elif record.status == 'إجازة' %}
                                                    <span class="badge badge-warning">{{ record.status }}</span>
                                                {% elif record.status == 'مهمة رسمية' %}
                                                    <span class="badge badge-info">{{ record.status }}</span>
                                                {% else %}
                                                    {{ record.status }}
                                                {% endif %}
                                            </td>
                                            <td class="action-buttons">
                                                <a href="{{ url_for('edit_attendance', attendance_id=record.id) }}" class="btn btn-info btn-sm" title="تعديل"><i class="fas fa-edit"></i></a>
                                                <form action="{{ url_for('delete_attendance', attendance_id=record.id) }}" method="POST" style="display:inline;" onsubmit="return confirm('هل أنت متأكد أنك تريد حذف سجل الحضور هذا؟');">
                                                    <button type="submit" class="btn btn-danger btn-sm" title="حذف"><i class="fas fa-trash"></i></button>
                                                </form>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info text-center" role="alert">
                            لا توجد سجلات حضور لعرضها.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.5.4/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#sidebarCollapse').on('click', function () {
                $('#sidebar').toggleClass('active');
            });
        });
    </script>
</body>
</html>