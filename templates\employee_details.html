<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بيانات الموظف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f0f2f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,.08);
            margin-top: 50px;
            margin-bottom: 50px;
        }
        .card {
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,.05);
            margin-bottom: 25px;
            border-radius: 10px;
            overflow: hidden;
        }
        .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            border-bottom: none;
            padding: 15px 20px;
            display: flex;
            align-items: center;
        }
        .card-header i {
            margin-left: 10px;
            font-size: 1.1rem;
        }
        .card-body {
            padding: 25px;
        }
        .employee-photo {
            width: 200px;
            height: 200px;
            border-radius: 10px;
            object-fit: cover;
            border: 5px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .employee-photo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .employee-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #343a40;
            margin-top: 10px;
            text-align: center;
        }
        .employee-job {
            font-size: 1.1rem;
            color: #6c757d;
            text-align: center;
            margin-bottom: 10px;
        }
        .employee-id {
            background-color: #e9ecef;
            padding: 5px 15px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            margin-top: 5px;
            font-size: 0.9rem;
        }
        .detail-row {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px dashed #e9ecef;
            padding-bottom: 15px;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .detail-label {
            font-weight: 600;
            color: #495057;
            width: 40%;
            padding-left: 15px;
        }
        .detail-value {
            color: #212529;
            width: 60%;
        }
        .btn {
            font-weight: bold;
            padding: 10px 20px;
            border-radius: 6px;
            transition: all 0.3s ease;
            margin-right: 10px;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }
        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }
        .btn-secondary:hover {
            background-color: #5a6268;
            border-color: #545b62;
        }
        .alert {
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .page-title {
            color: #343a40;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 700;
            position: relative;
            padding-bottom: 15px;
        }
        .page-title:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background-color: #007bff;
            border-radius: 2px;
        }
        .document-link {
            display: inline-flex;
            align-items: center;
            padding: 8px 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            color: #495057;
            text-decoration: none;
            margin-right: 10px;
            margin-bottom: 10px;
            transition: all 0.2s ease;
        }
        .document-link:hover {
            background-color: #e9ecef;
            color: #212529;
        }
        .document-link i {
            margin-left: 8px;
            color: #6c757d;
        }
        .documents-container {
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;
        }
        .no-documents {
            color: #6c757d;
            font-style: italic;
        }
        @media (max-width: 768px) {
            .detail-row {
                flex-direction: column;
            }
            .detail-label, .detail-value {
                width: 100%;
            }
            .detail-label {
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        <h2 class="page-title">بيانات الموظف</h2>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="employee-photo-container">
                    {% if employee.photo %}
                    <img src="{{ url_for('uploaded_file', filename=employee.photo) }}" alt="صورة الموظف" class="employee-photo">
                    {% else %}
                    <div class="employee-photo d-flex align-items-center justify-content-center bg-light">
                        <i class="fas fa-user fa-4x text-secondary"></i>
                    </div>
                    {% endif %}
                    <div class="employee-name">{{ employee.full_name }}</div>
                    <div class="employee-job">{{ employee.job_title }}</div>
                    <div class="employee-id">الرقم الوظيفي: {{ employee.employee_id }}</div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header"><i class="fas fa-user-alt"></i>البيانات الشخصية</div>
                    <div class="card-body">
                        <div class="detail-row">
                            <div class="detail-label">الاسم الكامل</div>
                            <div class="detail-value">{{ employee.full_name }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">الرقم الوطني</div>
                            <div class="detail-value">{{ employee.national_id }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">العمر</div>
                            <div class="detail-value">{{ employee.age }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">تاريخ الميلاد</div>
                            <div class="detail-value">{{ employee.date_of_birth }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">الجنس</div>
                            <div class="detail-value">{{ employee.gender }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">مكان الميلاد</div>
                            <div class="detail-value">
                                {% if employee.birth_place_city or employee.birth_place_region or employee.birth_place_municipality %}
                                    {{ employee.birth_place_city }} - {{ employee.birth_place_region }} - {{ employee.birth_place_municipality }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">الجنسية</div>
                            <div class="detail-value">{{ employee.nationality }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header"><i class="fas fa-briefcase"></i>البيانات الوظيفية</div>
                    <div class="card-body">
                        <div class="detail-row">
                            <div class="detail-label">الرقم الوظيفي</div>
                            <div class="detail-value">{{ employee.employee_id }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">المسمى الوظيفي</div>
                            <div class="detail-value">{{ employee.job_title }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">نوع التعيين</div>
                            <div class="detail-value">{{ employee.appointment_type }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">تاريخ التعيين</div>
                            <div class="detail-value">{{ employee.appointment_date }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">تاريخ بدء العمل</div>
                            <div class="detail-value">{{ employee.start_work_date }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">الدرجة الحالية</div>
                            <div class="detail-value">{{ employee.current_grade }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">عدد العلاوات</div>
                            <div class="detail-value">{{ employee.allowances_count }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">تاريخ آخر ترقية</div>
                            <div class="detail-value">{{ employee.last_promotion_date }}</div>
                        </div>
                        <div class="detail-row">
                            <div class="detail-label">سنوات الخدمة</div>
                            <div class="detail-value">{{ employee.years_of_service }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header"><i class="fas fa-file-alt"></i>المستندات</div>
                    <div class="card-body">
                        {% if documents %}
                            <div class="documents-container">
                                {% for document in documents %}
                                    <a href="{{ url_for('uploaded_file', filename=document.file_path) }}" class="document-link" target="_blank">
                                        <i class="fas fa-file-pdf"></i>
                                        {{ document.document_name }}
                                    </a>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="no-documents">لا توجد مستندات مرفقة</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-flex justify-content-between mt-4">
            <div>
                <a href="/edit_employee/{{ employee.id }}" class="btn btn-primary"><i class="fas fa-edit me-2"></i>تعديل بيانات الموظف</a>
                <a href="/add_document/{{ employee.id }}" class="btn btn-info"><i class="fas fa-file-upload me-2"></i>إضافة مستند</a>
            </div>
            <a href="/view_employees" class="btn btn-secondary"><i class="fas fa-arrow-circle-left me-2"></i>العودة لقائمة الموظفين</a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>