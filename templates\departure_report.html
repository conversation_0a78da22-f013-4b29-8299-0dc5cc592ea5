<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير المغادرة</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            font-family: 'Tajawal', 'Arial', sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 0;
            transition: all 0.3s ease;
        }
        .wrapper {
            display: flex;
            width: 100%;
            align-items: stretch;
        }
        #sidebar {
            min-width: 250px;
            max-width: 250px;
            background: #343a40;
            color: #fff;
            transition: all 0.3s;
            height: 100vh;
            position: fixed;
            z-index: 999;
        }
        #sidebar.active {
            margin-right: -250px;
        }
        #sidebar .sidebar-header {
            padding: 20px;
            background: #212529;
        }
        #sidebar ul.components {
            padding: 20px 0;
            border-bottom: 1px solid #4b545c;
        }
        #sidebar ul p {
            color: #fff;
            padding: 10px;
        }
        #sidebar ul li a {
            padding: 10px 15px;
            font-size: 1.1em;
            display: block;
            color: #fff;
            text-decoration: none;
            transition: all 0.3s;
        }
        #sidebar ul li a:hover {
            color: #343a40;
            background: #fff;
        }
        #sidebar ul li.active > a {
            color: #fff;
            background: #007bff;
        }
        #content {
            width: 100%;
            padding: 20px;
            min-height: 100vh;
            transition: all 0.3s;
            margin-right: 250px;
        }
        #content.active {
            margin-right: 0;
        }
        .card {
            border-radius: 0.75rem;
            box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
            margin-bottom: 1.5rem;
            border: none;
        }
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1.25rem 1.5rem;
            border-radius: 0.75rem 0.75rem 0 0 !important;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .card-body {
            padding: 1.5rem;
        }
        .btn {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.2s;
        }
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0069d9;
            border-color: #0062cc;
        }
        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
        }
        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }
        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
        .form-control {
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            border: 1px solid #ced4da;
        }
        .form-control:focus {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .table {
            background-color: #fff;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.8rem;
            letter-spacing: 0.05rem;
        }
        .table td, .table th {
            padding: 1rem;
            vertical-align: middle;
        }
        .badge {
            padding: 0.5rem 0.75rem;
            font-weight: 500;
            border-radius: 0.5rem;
        }
        .search-form {
            background-color: #fff;
            padding: 1.5rem;
            border-radius: 0.75rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }
        .report-stats {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        .stat-card {
            flex: 1;
            min-width: 200px;
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        }
        .stat-card .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .stat-card .stat-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        .stat-card .stat-label {
            font-size: 1rem;
            color: #6c757d;
        }
        .stat-card.today {
            border-top: 4px solid #28a745;
        }
        .stat-card.today .stat-icon {
            color: #28a745;
        }
        .stat-card.week {
            border-top: 4px solid #ffc107;
        }
        .stat-card.week .stat-icon {
            color: #ffc107;
        }
        .stat-card.month {
            border-top: 4px solid #17a2b8;
        }
        .stat-card.month .stat-icon {
            color: #17a2b8;
        }
        .stat-card.total {
            border-top: 4px solid #007bff;
        }
        .stat-card.total .stat-icon {
            color: #007bff;
        }
        .chart-container {
            background-color: #fff;
            border-radius: 0.75rem;
            padding: 1.5rem;
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }
        .chart-container h5 {
            margin-bottom: 1.5rem;
            text-align: center;
        }
        .action-buttons {
            white-space: nowrap;
        }
        .action-buttons .btn {
            padding: 0.375rem 0.75rem;
            margin: 0 0.25rem;
        }
        .print-btn {
            margin-bottom: 1rem;
        }
        @media print {
            #sidebar, .navbar, .search-form, .no-print {
                display: none !important;
            }
            #content {
                margin-right: 0 !important;
                padding: 0 !important;
            }
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }
            body {
                background-color: white !important;
            }
        }
        @media (max-width: 768px) {
            #sidebar {
                margin-right: -250px;
            }
            #sidebar.active {
                margin-right: 0;
            }
            #content {
                margin-right: 0;
            }
            #content.active {
                margin-right: 250px;
            }
            #sidebarCollapse span {
                display: none;
            }
        }
        .time-range {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        .heatmap-container {
            margin-top: 1.5rem;
            margin-bottom: 1.5rem;
        }
        .heatmap-day {
            display: flex;
            margin-bottom: 0.5rem;
        }
        .heatmap-hour {
            flex: 1;
            height: 30px;
            margin: 0 2px;
            border-radius: 3px;
            transition: all 0.3s ease;
        }
        .heatmap-hour:hover {
            transform: translateY(-3px);
        }
        .heatmap-legend {
            display: flex;
            justify-content: center;
            margin-top: 1rem;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 0 1rem;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="wrapper">
        <!-- Sidebar  -->
        <nav id="sidebar">
            <div class="sidebar-header">
                <h3>نظام إدارة الموظفين</h3>
            </div>

            <ul class="list-unstyled components">
                <li>
                    <a href="{{ url_for('dashboard') }}"><i class="fas fa-home"></i> الرئيسية</a>
                </li>
                <li>
                    <a href="{{ url_for('view_employees') }}"><i class="fas fa-users"></i> إدارة الموظفين</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departments') }}"><i class="fas fa-building"></i> إدارة الأقسام</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_attendance') }}"><i class="fas fa-check-circle"></i> إدارة الحضور</a>
                </li>
                <li>
                    <a href="{{ url_for('attendance_report_route') }}"><i class="fas fa-file-alt"></i> تقارير الحضور</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_departure') }}"><i class="fas fa-sign-out-alt"></i> إدارة المغادرة</a>
                </li>
                <li class="active">
                    <a href="{{ url_for('departure_report_route') }}"><i class="fas fa-file-export"></i> تقارير المغادرة</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_documents') }}"><i class="fas fa-file-alt"></i> الوثائق</a>
                </li>
                <li>
                    <a href="{{ url_for('manage_leaves') }}"><i class="fas fa-calendar-alt"></i> الإجازات</a>
                </li>
                <li>
                    <a href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                </li>
            </ul>
        </nav>

        <!-- Page Content  -->
        <div id="content">
            <nav class="navbar navbar-expand-lg navbar-light bg-light">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-info">
                        <i class="fas fa-align-right"></i>
                        <span>تبديل الشريط الجانبي</span>
                    </button>
                    <div class="ml-auto">
                        <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                    </div>
                </div>
            </nav>

            <h2 class="mb-4 text-center">تقارير المغادرة</h2>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- إحصائيات المغادرة -->
            <div class="report-stats">
                <div class="stat-card today">
                    <div class="stat-icon"><i class="fas fa-calendar-day"></i></div>
                    <div class="stat-value">{{ stats.today if stats else 0 }}</div>
                    <div class="stat-label">مغادرات اليوم</div>
                </div>
                <div class="stat-card week">
                    <div class="stat-icon"><i class="fas fa-calendar-week"></i></div>
                    <div class="stat-value">{{ stats.week if stats else 0 }}</div>
                    <div class="stat-label">مغادرات الأسبوع</div>
                </div>
                <div class="stat-card month">
                    <div class="stat-icon"><i class="fas fa-calendar-alt"></i></div>
                    <div class="stat-value">{{ stats.month if stats else 0 }}</div>
                    <div class="stat-label">مغادرات الشهر</div>
                </div>
                <div class="stat-card total">
                    <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
                    <div class="stat-value">{{ stats.total if stats else 0 }}</div>
                    <div class="stat-label">إجمالي المغادرات</div>
                </div>
            </div>

            <!-- زر الطباعة -->
            <div class="print-btn no-print">
                <button onclick="window.print()" class="btn btn-primary"><i class="fas fa-print"></i> طباعة التقرير</button>
                <a href="{{ url_for('export_departure_report_route') }}" class="btn btn-success"><i class="fas fa-file-excel"></i> تصدير إلى Excel</a>
            </div>

            <!-- نموذج البحث والتصفية -->
            <div class="search-form no-print">
                <form class="form-row" method="GET" action="{{ url_for('departure_report_route') }}">
                    <div class="form-group col-md-3">
                        <label for="employee_id">الموظف</label>
                        <select class="form-control" id="employee_id" name="employee_id">
                            <option value="">جميع الموظفين</option>
                            {% for employee in all_employees %}
                                <option value="{{ employee.id }}" {% if filter_employee_id == employee.id %}selected{% endif %}>{{ employee.full_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <label for="date_from">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ filter_date_from }}">
                    </div>
                    <div class="form-group col-md-2">
                        <label for="date_to">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ filter_date_to }}">
                    </div>
                    <div class="form-group col-md-3">
                        <label for="department_id">القسم</label>
                        <select class="form-control" id="department_id" name="department_id">
                            <option value="">جميع الأقسام</option>
                            {% for department in all_departments %}
                                <option value="{{ department.id }}" {% if filter_department_id == department.id %}selected{% endif %}>{{ department.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group col-md-2">
                        <label>وقت المغادرة</label>
                        <div class="time-range">
                            <input type="time" class="form-control" name="time_from" value="{{ filter_time_from }}">
                            <span>إلى</span>
                            <input type="time" class="form-control" name="time_to" value="{{ filter_time_to }}">
                        </div>
                    </div>
                    <div class="form-group col-md-12 text-center">
                        <button type="submit" class="btn btn-primary mx-2"><i class="fas fa-search"></i> بحث وتصفية</button>
                        <a href="{{ url_for('departure_report_route') }}" class="btn btn-secondary mx-2"><i class="fas fa-sync-alt"></i> مسح التصفية</a>
                    </div>
                </form>
            </div>

            <!-- الرسوم البيانية -->
            <div class="row">
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>توزيع المغادرات حسب الوقت</h5>
                        <canvas id="timeChart"></canvas>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="chart-container">
                        <h5>معدل المغادرات الشهري</h5>
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- خريطة حرارية لأوقات المغادرة -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-fire mr-2"></i> خريطة حرارية لأوقات المغادرة</h5>
                </div>
                <div class="card-body">
                    <div class="heatmap-container">
                        <div class="row">
                            <div class="col-md-1">
                                <div class="heatmap-days">
                                    <div class="heatmap-day-label" style="height: 30px; margin-bottom: 0.5rem; text-align: center;">الأحد</div>
                                    <div class="heatmap-day-label" style="height: 30px; margin-bottom: 0.5rem; text-align: center;">الإثنين</div>
                                    <div class="heatmap-day-label" style="height: 30px; margin-bottom: 0.5rem; text-align: center;">الثلاثاء</div>
                                    <div class="heatmap-day-label" style="height: 30px; margin-bottom: 0.5rem; text-align: center;">الأربعاء</div>
                                    <div class="heatmap-day-label" style="height: 30px; margin-bottom: 0.5rem; text-align: center;">الخميس</div>
                                </div>
                            </div>
                            <div class="col-md-11">
                                <div id="heatmap"></div>
                            </div>
                        </div>
                        <div class="heatmap-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: rgba(0, 123, 255, 0.1);"></div>
                                <span>قليل</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: rgba(0, 123, 255, 0.4);"></div>
                                <span>متوسط</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: rgba(0, 123, 255, 0.7);"></div>
                                <span>كثير</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background-color: rgba(0, 123, 255, 1);"></div>
                                <span>كثير جداً</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول تقرير المغادرة -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list mr-2"></i> تقرير المغادرة</h5>
                </div>
                <div class="card-body">
                    {% if departure_records %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الموظف</th>
                                        <th>الرقم الوظيفي</th>
                                        <th>القسم</th>
                                        <th>التاريخ</th>
                                        <th>وقت المغادرة</th>
                                        <th>سبب المغادرة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for record in departure_records %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ record.employee.full_name }}</td>
                                            <td>{{ record.employee.employee_id }}</td>
                                            <td>{{ record.employee.department.name if record.employee.department else 'غير محدد' }}</td>
                                            <td>{{ record.date.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ record.departure_time.strftime('%H:%M') }}</td>
                                            <td>{{ record.reason if record.reason else 'غير محدد' }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد سجلات مغادرة تطابق معايير البحث.
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- ملخص المغادرة حسب الموظف -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-clock mr-2"></i> ملخص المغادرة حسب الموظف</h5>
                </div>
                <div class="card-body">
                    {% if employee_summary %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>اسم الموظف</th>
                                        <th>الرقم الوظيفي</th>
                                        <th>القسم</th>
                                        <th>عدد المغادرات</th>
                                        <th>متوسط وقت المغادرة</th>
                                        <th>أبكر مغادرة</th>
                                        <th>أكثر أيام المغادرة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for summary in employee_summary %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ summary.employee_name }}</td>
                                            <td>{{ summary.employee_id }}</td>
                                            <td>{{ summary.department }}</td>
                                            <td>{{ summary.departure_count }}</td>
                                            <td>{{ summary.average_time }}</td>
                                            <td>{{ summary.earliest_time }}</td>
                                            <td>{{ summary.most_frequent_day }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            لا توجد بيانات ملخصة متاحة.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>
        $(document).ready(function () {
            $('#sidebarCollapse').on('click', function () {
                $('#sidebar, #content').toggleClass('active');
            });

            // إنشاء خريطة حرارية لأوقات المغادرة
            createHeatmap();
        });

        // رسم بياني لتوزيع المغادرات حسب الوقت
        var timeCtx = document.getElementById('timeChart').getContext('2d');
        var timeChart = new Chart(timeCtx, {
            type: 'bar',
            data: {
                labels: ['8-9', '9-10', '10-11', '11-12', '12-13', '13-14', '14-15', '15-16', '16-17'],
                datasets: [{
                    label: 'عدد المغادرات',
                    data: {{ time_stats|safe if time_stats else '[0, 0, 0, 0, 0, 0, 0, 0, 0]' }},
                    backgroundColor: '#17a2b8',
                    borderColor: '#17a2b8',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true
                        },
                        scaleLabel: {
                            display: true,
                            labelString: 'عدد المغادرات'
                        }
                    }],
                    xAxes: [{
                        scaleLabel: {
                            display: true,
                            labelString: 'الساعة'
                        }
                    }]
                },
                legend: {
                    display: false
                }
            }
        });

        // رسم بياني للمغادرات الشهرية
        var monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
        var monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
                datasets: [{
                    label: 'عدد المغادرات',
                    data: {{ monthly_stats|safe if monthly_stats else '[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]' }},
                    backgroundColor: 'rgba(0, 123, 255, 0.2)',
                    borderColor: '#007bff',
                    borderWidth: 2,
                    pointBackgroundColor: '#007bff',
                    pointBorderColor: '#fff',
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    yAxes: [{
                        ticks: {
                            beginAtZero: true
                        },
                        scaleLabel: {
                            display: true,
                            labelString: 'عدد المغادرات'
                        }
                    }]
                }
            }
        });

        // إنشاء خريطة حرارية لأوقات المغادرة
        function createHeatmap() {
            // بيانات وهمية للخريطة الحرارية - يمكن استبدالها ببيانات حقيقية من الخادم
            var heatmapData = {{ heatmap_data|safe if heatmap_data else '[
                [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9],
                [0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.1],
                [0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.1, 0.2],
                [0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 0.1, 0.2, 0.3],
                [0.5, 0.6, 0.7, 0.8, 0.9, 0.1, 0.2, 0.3, 0.4]
            ]' }};

            var heatmapContainer = document.getElementById('heatmap');
            var hours = ['8-9', '9-10', '10-11', '11-12', '12-13', '13-14', '14-15', '15-16', '16-17'];
            var days = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس'];

            // إنشاء صفوف الخريطة الحرارية
            for (var i = 0; i < days.length; i++) {
                var dayRow = document.createElement('div');
                dayRow.className = 'heatmap-day';

                for (var j = 0; j < hours.length; j++) {
                    var hourCell = document.createElement('div');
                    hourCell.className = 'heatmap-hour';
                    var intensity = heatmapData[i][j];
                    hourCell.style.backgroundColor = `rgba(0, 123, 255, ${intensity})`;
                    hourCell.title = `${days[i]} ${hours[j]}: ${Math.round(intensity * 100)}%`;
                    dayRow.appendChild(hourCell);
                }

                heatmapContainer.appendChild(dayRow);
            }

            // إضافة تسميات الساعات
            var hoursLabels = document.createElement('div');
            hoursLabels.className = 'heatmap-hours-labels d-flex justify-content-between mt-2';
            for (var k = 0; k < hours.length; k++) {
                var hourLabel = document.createElement('div');
                hourLabel.className = 'hour-label';
                hourLabel.textContent = hours[k];
                hourLabel.style.flex = '1';
                hourLabel.style.textAlign = 'center';
                hourLabel.style.fontSize = '0.8rem';
                hoursLabels.appendChild(hourLabel);
            }
            heatmapContainer.appendChild(hoursLabels);
        }
    </script>
</body>
</html>